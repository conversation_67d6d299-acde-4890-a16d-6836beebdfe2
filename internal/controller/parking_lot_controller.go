package controller

import (
	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime/types"
	api "github.com/smooth-inc/backend/api/generated"
	"github.com/smooth-inc/backend/internal/infra/http/response"
	"github.com/smooth-inc/backend/internal/infra/logger"
	"github.com/smooth-inc/backend/internal/usecase"
)

type ParkingLotController struct {
	parkingLotUsecase       usecase.ParkingLotUsecase
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase
	logger                  *logger.Logger
}

func NewParkingLotController(
	parkingLotUsecase usecase.ParkingLotUsecase,
	adminParkingLotConfigUsecase usecase.AdminParkingLotConfigUsecase,
	logger *logger.Logger,
) *ParkingLotController {
	return &ParkingLotController{
		parkingLotUsecase:            parkingLotUsecase,
		adminParkingLotConfigUsecase: adminParkingLotConfigUsecase,
		logger:                       logger,
	}
}

func (plc *ParkingLotController) GetParkingLots(c *gin.Context, params api.GetParkingLotsParams) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lots", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots",
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lots feature not implemented yet")
}

func (plc *ParkingLotController) GetParkingLotsLotId(c *gin.Context, lotId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot by ID", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots/{lotId}",
		"lot_id": lotId,
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lot by ID feature not implemented yet")
}

func (plc *ParkingLotController) GetParkingLotsLotIdAvailability(c *gin.Context, lotId types.UUID) {
	plc.logger.LogInfo(c.Request.Context(), "Getting parking lot availability", map[string]interface{}{
		"method": "GET",
		"path":   "/parking-lots/{lotId}/availability",
		"lot_id": lotId,
	})
	response.ServiceUnavailable(c, "NOT_IMPLEMENTED", "Get parking lot availability feature not implemented yet")
}