package repository

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/smooth-inc/backend/internal/domain"
)

type UserRepository interface {
	Create(ctx context.Context, user *domain.User) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.User, error)
	GetByEmail(ctx context.Context, email string) (*domain.User, error)
	GetByUsername(ctx context.Context, username string) (*domain.User, error)
	GetByEmailOrUsername(ctx context.Context, identifier string) (*domain.User, error)
	GetByStripeCustomerID(ctx context.Context, stripeCustomerID string) (*domain.User, error)
	Update(ctx context.Context, user *domain.User) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*domain.User, error)
	ListWithFilters(ctx context.Context, status *domain.UserStatus, role *domain.UserRole, search *string, emailVerified *bool, limit, offset int) ([]*domain.User, int, error)
	ExistsByEmail(ctx context.Context, email string) (bool, error)
	ExistsByUsername(ctx context.Context, username string) (bool, error)

	CreateUserWithVerificationToken(ctx context.Context, user *domain.User, verificationToken *domain.VerificationToken) error
	VerifyEmailWithSession(ctx context.Context, userID uuid.UUID, verificationToken *domain.VerificationToken, session *domain.UserSession) error
	ResetPasswordWithCleanup(ctx context.Context, userID uuid.UUID, newPasswordHash string, verificationToken *domain.VerificationToken) error
}

type UserSessionRepository interface {
	Create(ctx context.Context, session *domain.UserSession) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.UserSession, error)
	GetByRefreshTokenHash(ctx context.Context, tokenHash string) (*domain.UserSession, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.UserSession, error)
	GetByUserAndDevice(ctx context.Context, userID uuid.UUID, deviceID string) (*domain.UserSession, error)
	Update(ctx context.Context, session *domain.UserSession) error
	Delete(ctx context.Context, id uuid.UUID) error
	DeleteByUserID(ctx context.Context, userID uuid.UUID) error
	DeleteByUserAndDevice(ctx context.Context, userID uuid.UUID, deviceID string) error
	DeleteExpired(ctx context.Context) error
	CleanupExpiredSessions(ctx context.Context) (int, error)

	CreateOrUpdateSessionWithLogin(ctx context.Context, userID uuid.UUID, deviceID string, session *domain.UserSession, user *domain.User) (*domain.UserSession, error)
}

type VerificationTokenRepository interface {
	Create(ctx context.Context, token *domain.VerificationToken) error
	GetByTokenHash(ctx context.Context, tokenHash string) (*domain.VerificationToken, error)
	GetByUserIDAndType(ctx context.Context, userID uuid.UUID, tokenType domain.VerificationType) (*domain.VerificationToken, error)
	Update(ctx context.Context, token *domain.VerificationToken) error
	Delete(ctx context.Context, id uuid.UUID) error
	DeleteByUserIDAndType(ctx context.Context, userID uuid.UUID, tokenType domain.VerificationType) error
	DeleteExpired(ctx context.Context) error
	CleanupExpiredTokens(ctx context.Context) (int, error)

	ReplaceVerificationToken(ctx context.Context, userID uuid.UUID, tokenType domain.VerificationType, newToken *domain.VerificationToken) error
}

type PlateRepository interface {
	Create(ctx context.Context, plate *domain.Plate) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Plate, error)
	GetByPlateNumber(ctx context.Context, plateNumber string) (*domain.Plate, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.Plate, error)
	Update(ctx context.Context, plate *domain.Plate) error
	Delete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, limit, offset int) ([]*domain.Plate, error)
	ListWithFilters(ctx context.Context, userID *uuid.UUID, plateNumber *string, plateType *domain.PlateType, isActive *bool, limit, offset int) ([]*domain.Plate, int, error)
	ExistsByPlateNumber(ctx context.Context, plateNumber string) (bool, error)
	ExistsByPlateNumberForUser(ctx context.Context, plateNumber string, userID uuid.UUID) (bool, error)
}

type ParkingLotRepository interface {
	Create(ctx context.Context, lot *domain.ParkingLot) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, error)
	List(ctx context.Context, limit, offset int) ([]*domain.ParkingLot, error)
	ListWithFilters(ctx context.Context, status *domain.LotStatus, search *string, limit, offset int) ([]*domain.ParkingLot, int, error)
	SearchNearby(ctx context.Context, lat, lng float64, radius int, limit, offset int) ([]*domain.ParkingLot, error)
	Update(ctx context.Context, lot *domain.ParkingLot) error
	Delete(ctx context.Context, id uuid.UUID) error
	GetWithStats(ctx context.Context, id uuid.UUID) (*domain.ParkingLot, map[string]interface{}, error)
}

type SessionRepository interface {
	Create(ctx context.Context, session *domain.Session) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Session, error)
	GetActiveByPlateID(ctx context.Context, plateID uuid.UUID) (*domain.Session, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Session, error)
	Update(ctx context.Context, session *domain.Session) error
	Delete(ctx context.Context, id uuid.UUID) error
	ListWithFilters(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]*domain.Session, int, error)
}

type PaymentRepository interface {
	Create(ctx context.Context, payment *domain.Payment) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Payment, error)
	GetBySessionID(ctx context.Context, sessionID uuid.UUID) (*domain.Payment, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Payment, error)
	GetByStripePaymentIntentID(ctx context.Context, stripePaymentIntentID string) (*domain.Payment, error)
	Update(ctx context.Context, payment *domain.Payment) error
	Delete(ctx context.Context, id uuid.UUID) error
	ListWithFilters(ctx context.Context, filters map[string]interface{}, limit, offset int) ([]*domain.Payment, int, error)
}

type BookingRepository interface {
	Create(ctx context.Context, booking *domain.Booking) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Booking, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Booking, error)
	Update(ctx context.Context, booking *domain.Booking) error
	Delete(ctx context.Context, id uuid.UUID) error
	CheckConflict(ctx context.Context, parkingLotID uuid.UUID, startTime, endTime string) (bool, error)
}

type NotificationRepository interface {
	Create(ctx context.Context, notification *domain.Notification) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Notification, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, unreadOnly bool, limit, offset int) ([]*domain.Notification, error)
	Update(ctx context.Context, notification *domain.Notification) error
	Delete(ctx context.Context, id uuid.UUID) error
	MarkAsRead(ctx context.Context, id uuid.UUID) error
}

type ParkingLotConfigRepository interface {
	Create(ctx context.Context, config *domain.ParkingLotConfig) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.ParkingLotConfig, error)
	GetByParkingLotID(ctx context.Context, parkingLotID uuid.UUID, includeInactive bool) ([]*domain.ParkingLotConfig, error)
	GetActiveByParkingLotID(ctx context.Context, parkingLotID uuid.UUID, effectiveAt time.Time) (*domain.ParkingLotConfig, error)
	Update(ctx context.Context, config *domain.ParkingLotConfig) error
	Delete(ctx context.Context, id uuid.UUID) error
	DeactivateAllForParkingLot(ctx context.Context, parkingLotID uuid.UUID) error
}

type PaymentMethodRepository interface {
	Create(ctx context.Context, paymentMethod *domain.PaymentMethod) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.PaymentMethod, error)
	GetByUserID(ctx context.Context, userID uuid.UUID) ([]*domain.PaymentMethod, error)
	GetByStripePaymentMethodID(ctx context.Context, stripePaymentMethodID string) (*domain.PaymentMethod, error)
	GetDefaultByUserID(ctx context.Context, userID uuid.UUID) (*domain.PaymentMethod, error)
	Update(ctx context.Context, paymentMethod *domain.PaymentMethod) error
	Delete(ctx context.Context, id uuid.UUID) error
	SetAsDefault(ctx context.Context, id uuid.UUID, userID uuid.UUID) error
	UnsetDefaultForUser(ctx context.Context, userID uuid.UUID) error
}

type DisputeRepository interface {
	Create(ctx context.Context, dispute *domain.Dispute) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Dispute, error)
	GetByStripeDisputeID(ctx context.Context, stripeDisputeID string) (*domain.Dispute, error)
	GetByStripeChargeID(ctx context.Context, stripeChargeID string) (*domain.Dispute, error)
	GetByPaymentID(ctx context.Context, paymentID uuid.UUID) ([]*domain.Dispute, error)
	Update(ctx context.Context, dispute *domain.Dispute) error
	List(ctx context.Context, limit, offset int) ([]*domain.Dispute, error)
	ListByStatus(ctx context.Context, status domain.DisputeStatus, limit, offset int) ([]*domain.Dispute, error)
}

type InvoiceRepository interface {
	Create(ctx context.Context, invoice *domain.Invoice) error
	GetByID(ctx context.Context, id uuid.UUID) (*domain.Invoice, error)
	GetByStripeInvoiceID(ctx context.Context, stripeInvoiceID string) (*domain.Invoice, error)
	GetByStripeCustomerID(ctx context.Context, stripeCustomerID string, limit, offset int) ([]*domain.Invoice, error)
	GetByUserID(ctx context.Context, userID uuid.UUID, limit, offset int) ([]*domain.Invoice, error)
	Update(ctx context.Context, invoice *domain.Invoice) error
	List(ctx context.Context, limit, offset int) ([]*domain.Invoice, error)
	ListByStatus(ctx context.Context, status domain.InvoiceStatus, limit, offset int) ([]*domain.Invoice, error)
}
