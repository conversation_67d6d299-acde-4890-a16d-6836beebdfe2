# ============================================================================
# ADMIN APIs - Organized by Logical Groups
# ============================================================================

# ============================================================================
# 1. PARKING LOT MANAGEMENT (Full CRUD)
# ============================================================================

/admin/parking-lots:
  get:
    operationId: adminListParkingLots
    summary: List all parking lots with configuration
    tags:
      - Admin - Parking Lot Management
    security:
      - bearerAuth: []
    parameters:
      - name: page
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 20
      - name: search
        in: query
        required: false
        schema:
          type: string
        description: Search by parking lot name or address
      - name: status
        in: query
        required: false
        schema:
          type: string
          enum: [active, inactive, maintenance]
        description: Filter by parking lot status
    responses:
      "200":
        description: List of parking lots retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    $ref: "../components/schemas.yaml#/components/schemas/ParkingLot"
                pagination:
                  $ref: "../components/schemas.yaml#/components/schemas/Pagination"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"
  
  post:
    operationId: adminCreateParkingLot
    summary: Create a new parking lot with configuration
    tags:
      - Admin - Parking Lot Management
    security:
      - bearerAuth: []
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            required:
              - name
              - address
              - latitude
              - longitude
              - totalSpots
              - availableSpots
            properties:
              name:
                type: string
                minLength: 1
                maxLength: 100
              address:
                type: string
                minLength: 1
                maxLength: 255
              latitude:
                type: number
                format: double
                minimum: -90
                maximum: 90
              longitude:
                type: number
                format: double
                minimum: -180
                maximum: 180
              totalSpots:
                type: integer
                minimum: 1
              availableSpots:
                type: integer
                minimum: 0
    responses:
      "201":
        description: Parking lot created successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  $ref: "../components/schemas.yaml#/components/schemas/ParkingLot"
      "400":
        $ref: "../components/responses.yaml#/components/responses/BadRequestError"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

/admin/parking-lots/{id}:
  get:
    operationId: adminGetParkingLot
    summary: Get parking lot details with configuration
    tags:
      - Admin - Parking Lot Management
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      "200":
        description: Parking lot details retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  $ref: "../components/schemas.yaml#/components/schemas/ParkingLot"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"
  
  put:
    operationId: adminUpdateParkingLot
    summary: Update parking lot information
    tags:
      - Admin - Parking Lot Management
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              name:
                type: string
                minLength: 1
                maxLength: 100
              address:
                type: string
                minLength: 1
                maxLength: 255
              latitude:
                type: number
                format: double
                minimum: -90
                maximum: 90
              longitude:
                type: number
                format: double
                minimum: -180
                maximum: 180
              totalSpots:
                type: integer
                minimum: 1
              availableSpots:
                type: integer
                minimum: 0
    responses:
      "200":
        description: Parking lot updated successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  $ref: "../components/schemas.yaml#/components/schemas/ParkingLot"
      "400":
        $ref: "../components/responses.yaml#/components/responses/BadRequestError"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"
  
  delete:
    operationId: adminDeleteParkingLot
    summary: Delete parking lot (soft delete)
    tags:
      - Admin - Parking Lot Management
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      "200":
        description: Parking lot deleted successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                message:
                  type: string
                  example: "Parking lot deleted successfully"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

/admin/parking-lots/{id}/pricing-config:
  get:
    operationId: adminGetPricingConfig
    summary: Get pricing configuration for a parking lot
    tags:
      - Admin - Parking Configuration
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      "200":
        description: Pricing configuration retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  $ref: "../components/schemas.yaml#/components/schemas/ParkingLotConfig"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"
  
  put:
    operationId: adminUpdatePricingConfig
    summary: Update pricing configuration for a parking lot
    tags:
      - Admin - Parking Configuration
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: "../components/schemas.yaml#/components/schemas/ParkingLotConfig"
    responses:
      "200":
        description: Pricing configuration updated successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  $ref: "../components/schemas.yaml#/components/schemas/ParkingLotConfig"
      "400":
        $ref: "../components/responses.yaml#/components/responses/BadRequestError"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

# ============================================================================
# 2. USER MANAGEMENT (Read-only with detailed views)
# ============================================================================

/admin/users:
  get:
    operationId: adminListUsers
    summary: List all users with summarized information (read-only)
    tags:
      - Admin - User Management
    security:
      - bearerAuth: []
    parameters:
      - name: page
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 20
      - name: search
        in: query
        required: false
        schema:
          type: string
        description: Search by email or name
    responses:
      "200":
        description: List of users retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                      email:
                        type: string
                        format: email
                      fullName:
                        type: string
                      status:
                        type: string
                        enum: [active, suspended]
                      createdAt:
                        type: string
                        format: date-time
                      plateCount:
                        type: integer
                      paymentMethodCount:
                        type: integer
                      hasActiveSession:
                        type: boolean
                pagination:
                  $ref: "../components/schemas.yaml#/components/schemas/Pagination"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

/admin/users/{id}:
  get:
    operationId: adminGetUser
    summary: Get detailed user information including profile, plates, payment methods, session history (read-only)
    tags:
      - Admin - User Management
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      "200":
        description: User details retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    user:
                      $ref: "../components/schemas.yaml#/components/schemas/User"
                    plates:
                      type: array
                      items:
                        $ref: "../components/schemas.yaml#/components/schemas/Plate"
                    paymentMethods:
                      type: array
                      items:
                        $ref: "../components/schemas.yaml#/components/schemas/PaymentMethod"
                    activeSessions:
                      type: array
                      items:
                        $ref: "../components/schemas.yaml#/components/schemas/Session"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

# ============================================================================
# 3. PLATE MANAGEMENT (Read-only)
# ============================================================================

/admin/plates:
  get:
    operationId: adminListPlates
    summary: List all plates across all users with summarized user information (read-only)
    tags:
      - Admin - License Plate Management
    security:
      - bearerAuth: []
    parameters:
      - name: page
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 20
      - name: search
        in: query
        required: false
        schema:
          type: string
        description: Search by plate number
    responses:
      "200":
        description: List of plates retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                      plateNumber:
                        type: string
                      isActive:
                        type: boolean
                      isInSession:
                        type: boolean
                      createdAt:
                        type: string
                        format: date-time
                      user:
                        type: object
                        properties:
                          id:
                            type: string
                            format: uuid
                          email:
                            type: string
                            format: email
                          fullName:
                            type: string
                pagination:
                  $ref: "../components/schemas.yaml#/components/schemas/Pagination"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

/admin/plates/{id}:
  get:
    operationId: adminGetPlate
    summary: Show plate details with associated user information (read-only)
    tags:
      - Admin - License Plate Management
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      "200":
        description: Plate details retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    plate:
                      $ref: "../components/schemas.yaml#/components/schemas/Plate"
                    user:
                      $ref: "../components/schemas.yaml#/components/schemas/User"
                    currentSession:
                      $ref: "../components/schemas.yaml#/components/schemas/Session"
                      nullable: true
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

# ============================================================================
# 4. SESSION MANAGEMENT (Read-only)
# ============================================================================

/admin/sessions:
  get:
    operationId: adminListSessions
    summary: List all parking sessions across all users/plates with key information (read-only)
    tags:
      - Admin - Session Management
    security:
      - bearerAuth: []
    parameters:
      - name: page
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          default: 1
      - name: limit
        in: query
        required: false
        schema:
          type: integer
          minimum: 1
          maximum: 100
          default: 20
      - name: status
        in: query
        required: false
        schema:
          type: string
          enum: [active, completed, cancelled]
      - name: parkingLotId
        in: query
        required: false
        schema:
          type: string
          format: uuid
      - name: userId
        in: query
        required: false
        schema:
          type: string
          format: uuid
    responses:
      "200":
        description: List of sessions retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: string
                        format: uuid
                      parkingLot:
                        type: object
                        properties:
                          id:
                            type: string
                            format: uuid
                          name:
                            type: string
                      user:
                        type: object
                        properties:
                          id:
                            type: string
                            format: uuid
                          email:
                            type: string
                            format: email
                          fullName:
                            type: string
                      plate:
                        type: object
                        properties:
                          id:
                            type: string
                            format: uuid
                          plateNumber:
                            type: string
                      checkInTime:
                        type: string
                        format: date-time
                      checkOutTime:
                        type: string
                        format: date-time
                        nullable: true
                      duration:
                        type: string
                        description: Duration in human-readable format
                      totalAmount:
                        type: number
                        format: double
                      status:
                        type: string
                        enum: [active, completed, cancelled]
                pagination:
                  $ref: "../components/schemas.yaml#/components/schemas/Pagination"
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError"

/admin/sessions/{id}:
  get:
    operationId: adminGetSession
    summary: Get detailed session information (read-only)
    tags:
      - Admin - Session Management
    security:
      - bearerAuth: []
    parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      "200":
        description: Session details retrieved successfully
        content:
          application/json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    session:
                      $ref: "../components/schemas.yaml#/components/schemas/Session"
                    parkingLot:
                      $ref: "../components/schemas.yaml#/components/schemas/ParkingLot"
                    user:
                      $ref: "../components/schemas.yaml#/components/schemas/User"
                    plate:
                      $ref: "../components/schemas.yaml#/components/schemas/Plate"
                    payment:
                      $ref: "../components/schemas.yaml#/components/schemas/Payment"
                      nullable: true
      "401":
        $ref: "../components/responses.yaml#/components/responses/UnauthorizedError"
      "403":
        $ref: "../components/responses.yaml#/components/responses/ForbiddenError"
      "404":
        $ref: "../components/responses.yaml#/components/responses/NotFoundError"
      "500":
        $ref: "../components/responses.yaml#/components/responses/InternalServerError" 