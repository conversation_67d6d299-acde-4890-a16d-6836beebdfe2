// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"time"

	openapi_types "github.com/oapi-codegen/runtime/types"
)

const (
	BearerAuthScopes   = "BearerAuth.Scopes"
	HardwareAuthScopes = "HardwareAuth.Scopes"
)

// Defines values for BookingStatus.
const (
	BookingStatusCancelled BookingStatus = "cancelled"
	BookingStatusCompleted BookingStatus = "completed"
	BookingStatusConfirmed BookingStatus = "confirmed"
	BookingStatusNoShow    BookingStatus = "no_show"
)

// Defines values for DetectionDirection.
const (
	Entry DetectionDirection = "entry"
	Exit  DetectionDirection = "exit"
)

// Defines values for DetectionResponseAction.
const (
	EntryCreated   DetectionResponseAction = "entry_created"
	ExitRecorded   DetectionResponseAction = "exit_recorded"
	NoAction       DetectionResponseAction = "no_action"
	SessionUpdated DetectionResponseAction = "session_updated"
)

// Defines values for HardwareDetectionRequestStatus.
const (
	N0 HardwareDetectionRequestStatus = 0
	N1 HardwareDetectionRequestStatus = 1
)

// Defines values for LotStatus.
const (
	LotStatusActive      LotStatus = "active"
	LotStatusClosed      LotStatus = "closed"
	LotStatusMaintenance LotStatus = "maintenance"
)

// Defines values for NotificationType.
const (
	BookingReminder  NotificationType = "booking_reminder"
	OverstayWarning  NotificationType = "overstay_warning"
	Parked           NotificationType = "parked"
	PaymentCompleted NotificationType = "payment_completed"
	PriceAlert       NotificationType = "price_alert"
)

// Defines values for PaymentErrorResponseErrorCode.
const (
	CardDeclined      PaymentErrorResponseErrorCode = "card_declined"
	ExpiredCard       PaymentErrorResponseErrorCode = "expired_card"
	InsufficientFunds PaymentErrorResponseErrorCode = "insufficient_funds"
	NoPaymentMethod   PaymentErrorResponseErrorCode = "no_payment_method"
	ProcessingError   PaymentErrorResponseErrorCode = "processing_error"
)

// Defines values for PaymentMethodType.
const (
	Card   PaymentMethodType = "card"
	Paypay PaymentMethodType = "paypay"
)

// Defines values for PaymentSetupValidationResponseNextStep.
const (
	AddPaymentMethod        PaymentSetupValidationResponseNextStep = "add_payment_method"
	EnableAutoPayment       PaymentSetupValidationResponseNextStep = "enable_auto_payment"
	None                    PaymentSetupValidationResponseNextStep = "none"
	SetDefaultPaymentMethod PaymentSetupValidationResponseNextStep = "set_default_payment_method"
	UpdateExpiredCard       PaymentSetupValidationResponseNextStep = "update_expired_card"
)

// Defines values for PaymentStatus.
const (
	PaymentStatusCompleted  PaymentStatus = "completed"
	PaymentStatusFailed     PaymentStatus = "failed"
	PaymentStatusPending    PaymentStatus = "pending"
	PaymentStatusProcessing PaymentStatus = "processing"
	PaymentStatusRefunded   PaymentStatus = "refunded"
)

// Defines values for PlateType.
const (
	Commercial PlateType = "commercial"
	Military   PlateType = "military"
	Normal     PlateType = "normal"
	Rental     PlateType = "rental"
)

// Defines values for PricingRuleDays.
const (
	Fri PricingRuleDays = "Fri"
	Mon PricingRuleDays = "Mon"
	Sat PricingRuleDays = "Sat"
	Sun PricingRuleDays = "Sun"
	Thu PricingRuleDays = "Thu"
	Tue PricingRuleDays = "Tue"
	Wed PricingRuleDays = "Wed"
)

// Defines values for RefundResponseStatus.
const (
	RefundResponseStatusFailed    RefundResponseStatus = "failed"
	RefundResponseStatusPending   RefundResponseStatus = "pending"
	RefundResponseStatusSucceeded RefundResponseStatus = "succeeded"
)

// Defines values for RegisterRequestPreferredLanguage.
const (
	RegisterRequestPreferredLanguageEn RegisterRequestPreferredLanguage = "en"
	RegisterRequestPreferredLanguageJa RegisterRequestPreferredLanguage = "ja"
)

// Defines values for SessionStatus.
const (
	SessionStatusActive    SessionStatus = "active"
	SessionStatusCancelled SessionStatus = "cancelled"
	SessionStatusCompleted SessionStatus = "completed"
	SessionStatusError     SessionStatus = "error"
)

// Defines values for UpdateUserRequestPreferredLanguage.
const (
	UpdateUserRequestPreferredLanguageEn UpdateUserRequestPreferredLanguage = "en"
	UpdateUserRequestPreferredLanguageJa UpdateUserRequestPreferredLanguage = "ja"
)

// Defines values for UserPreferredLanguage.
const (
	En UserPreferredLanguage = "en"
	Ja UserPreferredLanguage = "ja"
)

// Defines values for UserRole.
const (
	UserRoleAdmin UserRole = "admin"
	UserRoleUser  UserRole = "user"
)

// Defines values for UserStatus.
const (
	UserStatusActive    UserStatus = "active"
	UserStatusPending   UserStatus = "pending"
	UserStatusSuspended UserStatus = "suspended"
)

// Defines values for GetParkingLotsParamsSort.
const (
	Distance GetParkingLotsParamsSort = "distance"
	Price    GetParkingLotsParamsSort = "price"
)

// ActivatePricingConfigRequest defines model for ActivatePricingConfigRequest.
type ActivatePricingConfigRequest struct {
	EffectiveFrom  time.Time  `json:"effectiveFrom"`
	EffectiveUntil *time.Time `json:"effectiveUntil,omitempty"`
}

// AdminParkingLotDetails defines model for AdminParkingLotDetails.
type AdminParkingLotDetails struct {
	Address                *string                 `json:"address,omitempty"`
	AverageSessionDuration *int                    `json:"averageSessionDuration,omitempty"`
	CloseTime              *string                 `json:"closeTime,omitempty"`
	Configurations         *[]ParkingLotConfig     `json:"configurations,omitempty"`
	ContactPhone           *string                 `json:"contactPhone,omitempty"`
	CreatedAt              *time.Time              `json:"createdAt,omitempty"`
	CurrentAvailability    *ParkingLotAvailability `json:"currentAvailability,omitempty"`

	// DailyMaxRate Maximum daily rate in JPY
	DailyMaxRate *int `json:"dailyMaxRate,omitempty"`

	// Distance Distance in meters (for search results)
	Distance *float64  `json:"distance,omitempty"`
	Features *[]string `json:"features,omitempty"`

	// FreeMinutes Free parking minutes
	FreeMinutes   *int `json:"freeMinutes,omitempty"`
	HeightLimitCm *int `json:"heightLimitCm,omitempty"`

	// HourlyRate Rate in JPY
	HourlyRate    *int                `json:"hourlyRate,omitempty"`
	Id            *openapi_types.UUID `json:"id,omitempty"`
	Images        *[]string           `json:"images,omitempty"`
	Is24h         *bool               `json:"is24h,omitempty"`
	Latitude      *float64            `json:"latitude,omitempty"`
	Longitude     *float64            `json:"longitude,omitempty"`
	Name          *string             `json:"name,omitempty"`
	OpenTime      *string             `json:"openTime,omitempty"`
	OperatorName  *string             `json:"operatorName,omitempty"`
	PeakHours     *[]string           `json:"peakHours,omitempty"`
	Status        *LotStatus          `json:"status,omitempty"`
	TotalRevenue  *int                `json:"totalRevenue,omitempty"`
	TotalSessions *int                `json:"totalSessions,omitempty"`
	TotalSpots    *int                `json:"totalSpots,omitempty"`
	UpdatedAt     *time.Time          `json:"updatedAt,omitempty"`
}

// AdminParkingLotSummary defines model for AdminParkingLotSummary.
type AdminParkingLotSummary struct {
	Address          *string             `json:"address,omitempty"`
	CreatedAt        *time.Time          `json:"createdAt,omitempty"`
	CurrentOccupancy *int                `json:"currentOccupancy,omitempty"`
	Id               *openapi_types.UUID `json:"id,omitempty"`
	Name             *string             `json:"name,omitempty"`
	Status           *LotStatus          `json:"status,omitempty"`
	TodayRevenue     *int                `json:"todayRevenue,omitempty"`
	TotalSpots       *int                `json:"totalSpots,omitempty"`
}

// AdminParkingLotsResponse defines model for AdminParkingLotsResponse.
type AdminParkingLotsResponse struct {
	Limit  *int                      `json:"limit,omitempty"`
	Lots   *[]AdminParkingLotSummary `json:"lots,omitempty"`
	Offset *int                      `json:"offset,omitempty"`
	Total  *int                      `json:"total,omitempty"`
}

// AdminPaymentDetails defines model for AdminPaymentDetails.
type AdminPaymentDetails struct {
	// Amount Amount in JPY
	Amount                *int                 `json:"amount,omitempty"`
	CardBrand             *string              `json:"cardBrand,omitempty"`
	CardLast4             *string              `json:"cardLast4,omitempty"`
	CreatedAt             *time.Time           `json:"createdAt,omitempty"`
	Currency              *string              `json:"currency,omitempty"`
	Disputes              *[]DisputeDetails    `json:"disputes,omitempty"`
	FailureReason         *string              `json:"failureReason,omitempty"`
	Id                    *openapi_types.UUID  `json:"id,omitempty"`
	InvoiceNumber         *string              `json:"invoiceNumber,omitempty"`
	PaidAt                *time.Time           `json:"paidAt,omitempty"`
	PaymentMethodType     *string              `json:"paymentMethodType,omitempty"`
	ReceiptUrl            *string              `json:"receiptUrl,omitempty"`
	Refunds               *[]RefundDetails     `json:"refunds,omitempty"`
	RetryCount            *int                 `json:"retryCount,omitempty"`
	Session               *AdminSessionSummary `json:"session,omitempty"`
	SessionId             *openapi_types.UUID  `json:"sessionId,omitempty"`
	Status                *PaymentStatus       `json:"status,omitempty"`
	StripePaymentIntentId *string              `json:"stripePaymentIntentId,omitempty"`
	StripePaymentLinkId   *string              `json:"stripePaymentLinkId,omitempty"`
	StripeStatus          *string              `json:"stripeStatus,omitempty"`
	UpdatedAt             *time.Time           `json:"updatedAt,omitempty"`
	User                  *AdminUserSummary    `json:"user,omitempty"`
	UserId                *openapi_types.UUID  `json:"userId,omitempty"`
}

// AdminPaymentSummary defines model for AdminPaymentSummary.
type AdminPaymentSummary struct {
	Amount                *int                `json:"amount,omitempty"`
	CreatedAt             *time.Time          `json:"createdAt,omitempty"`
	Id                    *openapi_types.UUID `json:"id,omitempty"`
	ParkingLotName        *string             `json:"parkingLotName,omitempty"`
	PaymentMethod         *string             `json:"paymentMethod,omitempty"`
	PlateNumber           *string             `json:"plateNumber,omitempty"`
	SessionId             *openapi_types.UUID `json:"sessionId,omitempty"`
	Status                *PaymentStatus      `json:"status,omitempty"`
	StripePaymentIntentId *string             `json:"stripePaymentIntentId,omitempty"`
	UserId                *openapi_types.UUID `json:"userId,omitempty"`
	UserName              *string             `json:"userName,omitempty"`
}

// AdminPaymentsResponse defines model for AdminPaymentsResponse.
type AdminPaymentsResponse struct {
	Limit       *int                   `json:"limit,omitempty"`
	Offset      *int                   `json:"offset,omitempty"`
	Payments    *[]AdminPaymentSummary `json:"payments,omitempty"`
	Total       *int                   `json:"total,omitempty"`
	TotalAmount *int                   `json:"totalAmount,omitempty"`
}

// AdminPlateDetails defines model for AdminPlateDetails.
type AdminPlateDetails struct {
	// Classification Classification number (123)
	Classification *string    `json:"classification,omitempty"`
	CreatedAt      *time.Time `json:"createdAt,omitempty"`

	// Hiragana Hiragana character (あ)
	Hiragana *string             `json:"hiragana,omitempty"`
	Id       *openapi_types.UUID `json:"id,omitempty"`
	IsActive *bool               `json:"isActive,omitempty"`
	LastUsed *time.Time          `json:"lastUsed,omitempty"`

	// PlateNumber Full generated plate number
	PlateNumber    *string    `json:"plateNumber,omitempty"`
	PlateType      *PlateType `json:"plateType,omitempty"`
	RecentSessions *[]Session `json:"recentSessions,omitempty"`

	// Region Japanese region (品川)
	Region *string `json:"region,omitempty"`

	// SerialNumber Serial number (1234)
	SerialNumber  *string             `json:"serialNumber,omitempty"`
	TotalPayments *int                `json:"totalPayments,omitempty"`
	TotalSessions *int                `json:"totalSessions,omitempty"`
	UpdatedAt     *time.Time          `json:"updatedAt,omitempty"`
	User          *AdminUserSummary   `json:"user,omitempty"`
	UserId        *openapi_types.UUID `json:"userId,omitempty"`
}

// AdminPlateSummary defines model for AdminPlateSummary.
type AdminPlateSummary struct {
	CreatedAt     *time.Time          `json:"createdAt,omitempty"`
	Id            *openapi_types.UUID `json:"id,omitempty"`
	IsActive      *bool               `json:"isActive,omitempty"`
	LastUsed      *time.Time          `json:"lastUsed,omitempty"`
	PlateNumber   *string             `json:"plateNumber,omitempty"`
	PlateType     *PlateType          `json:"plateType,omitempty"`
	TotalSessions *int                `json:"totalSessions,omitempty"`
	UserEmail     *string             `json:"userEmail,omitempty"`
	UserId        *openapi_types.UUID `json:"userId,omitempty"`
	UserName      *string             `json:"userName,omitempty"`
}

// AdminPlatesResponse defines model for AdminPlatesResponse.
type AdminPlatesResponse struct {
	Limit  *int                 `json:"limit,omitempty"`
	Offset *int                 `json:"offset,omitempty"`
	Plates *[]AdminPlateSummary `json:"plates,omitempty"`
	Total  *int                 `json:"total,omitempty"`
}

// AdminSessionDetails defines model for AdminSessionDetails.
type AdminSessionDetails struct {
	// Amount Amount in JPY
	Amount              *int                `json:"amount,omitempty"`
	CreatedAt           *time.Time          `json:"createdAt,omitempty"`
	DetectionConfidence *float64            `json:"detectionConfidence,omitempty"`
	DiscountAmount      *int                `json:"discountAmount,omitempty"`
	DurationMinutes     *int                `json:"durationMinutes,omitempty"`
	EntryImageUrl       *string             `json:"entryImageUrl,omitempty"`
	EntryTime           *time.Time          `json:"entryTime,omitempty"`
	ErrorMessage        *string             `json:"errorMessage,omitempty"`
	ExitImageUrl        *string             `json:"exitImageUrl,omitempty"`
	ExitTime            *time.Time          `json:"exitTime,omitempty"`
	FeeBreakdown        *FeeBreakdown       `json:"feeBreakdown,omitempty"`
	Id                  *openapi_types.UUID `json:"id,omitempty"`
	IsPaid              *bool               `json:"isPaid,omitempty"`
	ManualOverride      *bool               `json:"manualOverride,omitempty"`
	ParkingLot          *ParkingLot         `json:"parkingLot,omitempty"`
	ParkingLotId        *openapi_types.UUID `json:"parkingLotId,omitempty"`
	Payment             *Payment            `json:"payment,omitempty"`
	Plate               *Plate              `json:"plate,omitempty"`
	PlateId             *openapi_types.UUID `json:"plateId,omitempty"`
	Status              *SessionStatus      `json:"status,omitempty"`
	UpdatedAt           *time.Time          `json:"updatedAt,omitempty"`
	User                *AdminUserSummary   `json:"user,omitempty"`
	UserId              *openapi_types.UUID `json:"userId,omitempty"`
}

// AdminSessionSummary defines model for AdminSessionSummary.
type AdminSessionSummary struct {
	Amount          *int                `json:"amount,omitempty"`
	CreatedAt       *time.Time          `json:"createdAt,omitempty"`
	DurationMinutes *int                `json:"durationMinutes,omitempty"`
	EntryTime       *time.Time          `json:"entryTime,omitempty"`
	ExitTime        *time.Time          `json:"exitTime,omitempty"`
	Id              *openapi_types.UUID `json:"id,omitempty"`
	IsPaid          *bool               `json:"isPaid,omitempty"`
	ParkingLotName  *string             `json:"parkingLotName,omitempty"`
	PlateNumber     *string             `json:"plateNumber,omitempty"`
	Status          *SessionStatus      `json:"status,omitempty"`
	UserId          *openapi_types.UUID `json:"userId,omitempty"`
	UserName        *string             `json:"userName,omitempty"`
}

// AdminSessionsResponse defines model for AdminSessionsResponse.
type AdminSessionsResponse struct {
	Limit    *int                   `json:"limit,omitempty"`
	Offset   *int                   `json:"offset,omitempty"`
	Sessions *[]AdminSessionSummary `json:"sessions,omitempty"`
	Total    *int                   `json:"total,omitempty"`
}

// AdminUpdateSessionRequest defines model for AdminUpdateSessionRequest.
type AdminUpdateSessionRequest struct {
	Amount         *int           `json:"amount,omitempty"`
	DiscountAmount *int           `json:"discountAmount,omitempty"`
	ExitTime       *time.Time     `json:"exitTime,omitempty"`
	Status         *SessionStatus `json:"status,omitempty"`
}

// AdminUpdateUserRequest defines model for AdminUpdateUserRequest.
type AdminUpdateUserRequest struct {
	AutoPaymentEnabled *bool       `json:"autoPaymentEnabled,omitempty"`
	Email              *string     `json:"email,omitempty"`
	EmailVerified      *bool       `json:"emailVerified,omitempty"`
	Name               *string     `json:"name,omitempty"`
	Phone              *string     `json:"phone,omitempty"`
	Role               *UserRole   `json:"role,omitempty"`
	Status             *UserStatus `json:"status,omitempty"`
	Username           *string     `json:"username,omitempty"`
}

// AdminUserDetails defines model for AdminUserDetails.
type AdminUserDetails struct {
	AutoPaymentEnabled *bool               `json:"autoPaymentEnabled,omitempty"`
	CreatedAt          *time.Time          `json:"createdAt,omitempty"`
	Email              *string             `json:"email,omitempty"`
	EmailVerified      *bool               `json:"emailVerified,omitempty"`
	Id                 *openapi_types.UUID `json:"id,omitempty"`
	LastLoginAt        *time.Time          `json:"lastLoginAt,omitempty"`
	Name               *string             `json:"name,omitempty"`
	Phone              *string             `json:"phone,omitempty"`
	Plates             *[]Plate            `json:"plates,omitempty"`
	Role               *UserRole           `json:"role,omitempty"`
	Status             *UserStatus         `json:"status,omitempty"`
	StripeCustomerId   *string             `json:"stripeCustomerId,omitempty"`
	TotalPayments      *int                `json:"totalPayments,omitempty"`
	TotalSessions      *int                `json:"totalSessions,omitempty"`
	TotalSpent         *int                `json:"totalSpent,omitempty"`
	UpdatedAt          *time.Time          `json:"updatedAt,omitempty"`
	Username           *string             `json:"username,omitempty"`
}

// AdminUserSummary defines model for AdminUserSummary.
type AdminUserSummary struct {
	CreatedAt     *time.Time          `json:"createdAt,omitempty"`
	Email         *string             `json:"email,omitempty"`
	EmailVerified *bool               `json:"emailVerified,omitempty"`
	Id            *openapi_types.UUID `json:"id,omitempty"`
	LastLoginAt   *time.Time          `json:"lastLoginAt,omitempty"`
	Name          *string             `json:"name,omitempty"`
	Role          *UserRole           `json:"role,omitempty"`
	Status        *UserStatus         `json:"status,omitempty"`
	Username      *string             `json:"username,omitempty"`
}

// AdminUsersResponse defines model for AdminUsersResponse.
type AdminUsersResponse struct {
	Limit  *int                `json:"limit,omitempty"`
	Offset *int                `json:"offset,omitempty"`
	Total  *int                `json:"total,omitempty"`
	Users  *[]AdminUserSummary `json:"users,omitempty"`
}

// AuthResponse defines model for AuthResponse.
type AuthResponse struct {
	AccessToken *string `json:"accessToken,omitempty"`
	ExpiresIn   *int    `json:"expiresIn,omitempty"`

	// PaymentSetupRequired Whether payment setup is required
	PaymentSetupRequired *bool                `json:"paymentSetupRequired,omitempty"`
	RefreshToken         *string              `json:"refreshToken,omitempty"`
	SetupIntent          *SetupIntentResponse `json:"setupIntent,omitempty"`
	User                 *User                `json:"user,omitempty"`
}

// AutoPaymentRequest defines model for AutoPaymentRequest.
type AutoPaymentRequest struct {
	// Amount Calculated parking fee in JPY
	Amount int `json:"amount"`

	// CalculatedFee Fee calculation breakdown
	CalculatedFee struct {
		BaseFee  *int `json:"baseFee,omitempty"`
		FinalFee *int `json:"finalFee,omitempty"`
	} `json:"calculatedFee"`

	// GenerateReceipt Generate and send receipt automatically
	GenerateReceipt *bool `json:"generateReceipt,omitempty"`

	// SendNotification Send payment completion notification
	SendNotification *bool `json:"sendNotification,omitempty"`

	// UseDefaultPaymentMethod Use user's default payment method
	UseDefaultPaymentMethod *bool `json:"useDefaultPaymentMethod,omitempty"`
}

// AutoPaymentResponse defines model for AutoPaymentResponse.
type AutoPaymentResponse struct {
	Amount                *int                `json:"amount,omitempty"`
	CardBrand             *string             `json:"cardBrand,omitempty"`
	CardLast4             *string             `json:"cardLast4,omitempty"`
	Currency              *string             `json:"currency,omitempty"`
	InvoiceNumber         *string             `json:"invoiceNumber,omitempty"`
	Message               *string             `json:"message,omitempty"`
	PaidAt                *time.Time          `json:"paidAt,omitempty"`
	PaymentId             *openapi_types.UUID `json:"paymentId,omitempty"`
	PaymentMethodType     *string             `json:"paymentMethodType,omitempty"`
	ReceiptUrl            *string             `json:"receiptUrl,omitempty"`
	Status                *PaymentStatus      `json:"status,omitempty"`
	StripePaymentIntentId *string             `json:"stripePaymentIntentId,omitempty"`
	Success               *bool               `json:"success,omitempty"`
}

// Booking defines model for Booking.
type Booking struct {
	CancellationFee *int                `json:"cancellationFee,omitempty"`
	CancelledAt     *time.Time          `json:"cancelledAt,omitempty"`
	CreatedAt       *time.Time          `json:"createdAt,omitempty"`
	EndTime         *time.Time          `json:"endTime,omitempty"`
	HourlyRate      *int                `json:"hourlyRate,omitempty"`
	Id              *openapi_types.UUID `json:"id,omitempty"`
	ParkingLot      *ParkingLot         `json:"parkingLot,omitempty"`
	ParkingLotId    *openapi_types.UUID `json:"parkingLotId,omitempty"`
	Plate           *Plate              `json:"plate,omitempty"`
	PlateId         *openapi_types.UUID `json:"plateId,omitempty"`
	StartTime       *time.Time          `json:"startTime,omitempty"`
	Status          *BookingStatus      `json:"status,omitempty"`
	TotalAmount     *int                `json:"totalAmount,omitempty"`
	UpdatedAt       *time.Time          `json:"updatedAt,omitempty"`
	UserId          *openapi_types.UUID `json:"userId,omitempty"`
}

// BookingStatus defines model for BookingStatus.
type BookingStatus string

// ChangePasswordRequest defines model for ChangePasswordRequest.
type ChangePasswordRequest struct {
	// CurrentPassword Current password
	CurrentPassword string `json:"currentPassword"`

	// NewPassword New password (minimum 8 characters)
	NewPassword string `json:"newPassword"`
}

// CreateBookingRequest defines model for CreateBookingRequest.
type CreateBookingRequest struct {
	EndTime      time.Time          `json:"endTime"`
	ParkingLotId openapi_types.UUID `json:"parkingLotId"`
	PlateId      openapi_types.UUID `json:"plateId"`
	StartTime    time.Time          `json:"startTime"`
}

// CreateParkingLotRequest defines model for CreateParkingLotRequest.
type CreateParkingLotRequest struct {
	Address       string    `json:"address"`
	CloseTime     *string   `json:"closeTime,omitempty"`
	ContactPhone  *string   `json:"contactPhone,omitempty"`
	DailyMaxRate  *int      `json:"dailyMaxRate,omitempty"`
	Features      *[]string `json:"features,omitempty"`
	FreeMinutes   *int      `json:"freeMinutes,omitempty"`
	HeightLimitCm *int      `json:"heightLimitCm,omitempty"`
	HourlyRate    *int      `json:"hourlyRate,omitempty"`
	Is24h         *bool     `json:"is24h,omitempty"`
	Latitude      float64   `json:"latitude"`
	Longitude     float64   `json:"longitude"`
	Name          string    `json:"name"`
	OpenTime      *string   `json:"openTime,omitempty"`
	OperatorName  *string   `json:"operatorName,omitempty"`
	TotalSpots    int       `json:"totalSpots"`
}

// CreatePlateRequest defines model for CreatePlateRequest.
type CreatePlateRequest struct {
	Classification string     `json:"classification"`
	Hiragana       string     `json:"hiragana"`
	PlateType      *PlateType `json:"plateType,omitempty"`
	Region         string     `json:"region"`
	SerialNumber   string     `json:"serialNumber"`
}

// CreatePricingConfigRequest defines model for CreatePricingConfigRequest.
type CreatePricingConfigRequest struct {
	ConfigName   string       `json:"configName"`
	PricingRules PricingRules `json:"pricingRules"`
}

// DefaultPaymentMethodResponse defines model for DefaultPaymentMethodResponse.
type DefaultPaymentMethodResponse struct {
	Message         *string `json:"message,omitempty"`
	PaymentMethodId *string `json:"paymentMethodId,omitempty"`
	Success         *bool   `json:"success,omitempty"`
}

// DetectionDirection defines model for DetectionDirection.
type DetectionDirection string

// DetectionResponse defines model for DetectionResponse.
type DetectionResponse struct {
	Action *DetectionResponseAction `json:"action,omitempty"`

	// CalculatedFee Calculated parking fee in JPY (for exit actions)
	CalculatedFee *int    `json:"calculatedFee,omitempty"`
	Message       *string `json:"message,omitempty"`

	// ParkingDuration Total parking duration in minutes (for exit actions)
	ParkingDuration *int                 `json:"parkingDuration,omitempty"`
	Payment         *AutoPaymentResponse `json:"payment,omitempty"`

	// PlateNumber Generated full plate number
	PlateNumber *string             `json:"plateNumber,omitempty"`
	SessionId   *openapi_types.UUID `json:"sessionId,omitempty"`
	Success     *bool               `json:"success,omitempty"`
}

// DetectionResponseAction defines model for DetectionResponse.Action.
type DetectionResponseAction string

// DisputeDetails defines model for DisputeDetails.
type DisputeDetails struct {
	Amount          *int                    `json:"amount,omitempty"`
	CreatedAt       *time.Time              `json:"createdAt,omitempty"`
	Evidence        *map[string]interface{} `json:"evidence,omitempty"`
	Id              *openapi_types.UUID     `json:"id,omitempty"`
	Reason          *string                 `json:"reason,omitempty"`
	Status          *string                 `json:"status,omitempty"`
	StripeDisputeId *string                 `json:"stripeDisputeId,omitempty"`
}

// EmailVerificationRequest defines model for EmailVerificationRequest.
type EmailVerificationRequest struct {
	// Token Email verification token from magic link
	Token string `json:"token"`
}

// ErrorResponse defines model for ErrorResponse.
type ErrorResponse struct {
	Code      *string                 `json:"code,omitempty"`
	Details   *map[string]interface{} `json:"details,omitempty"`
	Error     *string                 `json:"error,omitempty"`
	Message   *string                 `json:"message,omitempty"`
	Timestamp *time.Time              `json:"timestamp,omitempty"`
}

// FeeBreakdown defines model for FeeBreakdown.
type FeeBreakdown struct {
	BaseFee         *int `json:"baseFee,omitempty"`
	DailyCapApplied *int `json:"dailyCapApplied,omitempty"`
	DiscountAmount  *int `json:"discountAmount,omitempty"`
	FinalFee        *int `json:"finalFee,omitempty"`
	NightCapApplied *int `json:"nightCapApplied,omitempty"`
	OverrideFee     *int `json:"overrideFee,omitempty"`
}

// FeeCalculationPreviewRequest defines model for FeeCalculationPreviewRequest.
type FeeCalculationPreviewRequest struct {
	PricingRules PricingRules      `json:"pricingRules"`
	Scenarios    []ParkingScenario `json:"scenarios"`
}

// FeeCalculationPreviewResponse defines model for FeeCalculationPreviewResponse.
type FeeCalculationPreviewResponse struct {
	Scenarios *[]ScenarioResult `json:"scenarios,omitempty"`
}

// ForgotPasswordRequest defines model for ForgotPasswordRequest.
type ForgotPasswordRequest struct {
	Email openapi_types.Email `json:"email"`
}

// ForgotPasswordResponse defines model for ForgotPasswordResponse.
type ForgotPasswordResponse struct {
	Message *string `json:"message,omitempty"`
}

// HardwareDetectionRequest defines model for HardwareDetectionRequest.
type HardwareDetectionRequest struct {
	// N3L 3-digit classification number
	N3L string `json:"3L"`

	// C1 City value (Japanese region like 品川)
	C1 string `json:"C1"`

	// H1 1 letter Hiragana or army-related letter (E,H,K,M,T,Y)
	H1 string `json:"H1"`

	// ID 4-digit serial number
	ID string `json:"ID"`

	// Confidence Detection confidence score
	Confidence *float64 `json:"confidence,omitempty"`

	// ImageUrl Optional image URL from detection
	ImageUrl *string `json:"imageUrl,omitempty"`

	// Parkingid 6-digit parking lot identifier
	Parkingid string `json:"parkingid"`

	// Status 0: started to park (entry), 1: left the parking lot (exit)
	Status HardwareDetectionRequestStatus `json:"status"`

	// Ts Timestamp of detection
	Ts time.Time `json:"ts"`
}

// HardwareDetectionRequestStatus 0: started to park (entry), 1: left the parking lot (exit)
type HardwareDetectionRequestStatus int

// LoginRequest defines model for LoginRequest.
type LoginRequest struct {
	// Identifier Username or email address
	Identifier string `json:"identifier"`

	// Password User password
	Password string `json:"password"`
}

// LotStatus defines model for LotStatus.
type LotStatus string

// MessageResponse defines model for MessageResponse.
type MessageResponse struct {
	// Message Response message
	Message *string `json:"message,omitempty"`
	Success *bool   `json:"success,omitempty"`
}

// NightCap defines model for NightCap.
type NightCap struct {
	Cap   int    `json:"cap"`
	End   string `json:"end"`
	Start string `json:"start"`
}

// Notification defines model for Notification.
type Notification struct {
	CreatedAt    *time.Time          `json:"createdAt,omitempty"`
	Id           *openapi_types.UUID `json:"id,omitempty"`
	IsSent       *bool               `json:"isSent,omitempty"`
	Message      *string             `json:"message,omitempty"`
	ParkingLotId *openapi_types.UUID `json:"parkingLotId,omitempty"`
	PaymentId    *openapi_types.UUID `json:"paymentId,omitempty"`
	SentAt       *time.Time          `json:"sentAt,omitempty"`
	SessionId    *openapi_types.UUID `json:"sessionId,omitempty"`
	Title        *string             `json:"title,omitempty"`
	Type         *NotificationType   `json:"type,omitempty"`
	UpdatedAt    *time.Time          `json:"updatedAt,omitempty"`
	UserId       *openapi_types.UUID `json:"userId,omitempty"`
}

// NotificationType defines model for NotificationType.
type NotificationType string

// ParkingLot defines model for ParkingLot.
type ParkingLot struct {
	Address             *string                 `json:"address,omitempty"`
	CloseTime           *string                 `json:"closeTime,omitempty"`
	ContactPhone        *string                 `json:"contactPhone,omitempty"`
	CreatedAt           *time.Time              `json:"createdAt,omitempty"`
	CurrentAvailability *ParkingLotAvailability `json:"currentAvailability,omitempty"`

	// DailyMaxRate Maximum daily rate in JPY
	DailyMaxRate *int `json:"dailyMaxRate,omitempty"`

	// Distance Distance in meters (for search results)
	Distance *float64  `json:"distance,omitempty"`
	Features *[]string `json:"features,omitempty"`

	// FreeMinutes Free parking minutes
	FreeMinutes   *int `json:"freeMinutes,omitempty"`
	HeightLimitCm *int `json:"heightLimitCm,omitempty"`

	// HourlyRate Rate in JPY
	HourlyRate   *int                `json:"hourlyRate,omitempty"`
	Id           *openapi_types.UUID `json:"id,omitempty"`
	Images       *[]string           `json:"images,omitempty"`
	Is24h        *bool               `json:"is24h,omitempty"`
	Latitude     *float64            `json:"latitude,omitempty"`
	Longitude    *float64            `json:"longitude,omitempty"`
	Name         *string             `json:"name,omitempty"`
	OpenTime     *string             `json:"openTime,omitempty"`
	OperatorName *string             `json:"operatorName,omitempty"`
	Status       *LotStatus          `json:"status,omitempty"`
	TotalSpots   *int                `json:"totalSpots,omitempty"`
	UpdatedAt    *time.Time          `json:"updatedAt,omitempty"`
}

// ParkingLotAvailability defines model for ParkingLotAvailability.
type ParkingLotAvailability struct {
	AvailableSpots *int       `json:"availableSpots,omitempty"`
	LastUpdated    *time.Time `json:"lastUpdated,omitempty"`
	OccupancyRate  *float64   `json:"occupancyRate,omitempty"`
	TotalSpots     *int       `json:"totalSpots,omitempty"`
}

// ParkingLotConfig defines model for ParkingLotConfig.
type ParkingLotConfig struct {
	ConfigName     *string             `json:"configName,omitempty"`
	CreatedAt      *time.Time          `json:"createdAt,omitempty"`
	CreatedBy      *openapi_types.UUID `json:"createdBy,omitempty"`
	EffectiveFrom  *time.Time          `json:"effectiveFrom,omitempty"`
	EffectiveUntil *time.Time          `json:"effectiveUntil,omitempty"`
	Id             *openapi_types.UUID `json:"id,omitempty"`
	IsActive       *bool               `json:"isActive,omitempty"`
	ParkingLotId   *openapi_types.UUID `json:"parkingLotId,omitempty"`
	PricingRules   *PricingRules       `json:"pricingRules,omitempty"`
	UpdatedAt      *time.Time          `json:"updatedAt,omitempty"`
}

// ParkingLotsResponse defines model for ParkingLotsResponse.
type ParkingLotsResponse struct {
	Limit  *int          `json:"limit,omitempty"`
	Lots   *[]ParkingLot `json:"lots,omitempty"`
	Offset *int          `json:"offset,omitempty"`
	Total  *int          `json:"total,omitempty"`
}

// ParkingScenario defines model for ParkingScenario.
type ParkingScenario struct {
	EntryTime time.Time `json:"entryTime"`
	ExitTime  time.Time `json:"exitTime"`
	Name      string    `json:"name"`
}

// Payment defines model for Payment.
type Payment struct {
	// Amount Amount in JPY
	Amount                *int                `json:"amount,omitempty"`
	CardBrand             *string             `json:"cardBrand,omitempty"`
	CardLast4             *string             `json:"cardLast4,omitempty"`
	CreatedAt             *time.Time          `json:"createdAt,omitempty"`
	Currency              *string             `json:"currency,omitempty"`
	FailureReason         *string             `json:"failureReason,omitempty"`
	Id                    *openapi_types.UUID `json:"id,omitempty"`
	InvoiceNumber         *string             `json:"invoiceNumber,omitempty"`
	PaidAt                *time.Time          `json:"paidAt,omitempty"`
	PaymentMethodType     *string             `json:"paymentMethodType,omitempty"`
	ReceiptUrl            *string             `json:"receiptUrl,omitempty"`
	RetryCount            *int                `json:"retryCount,omitempty"`
	Session               *Session            `json:"session,omitempty"`
	SessionId             *openapi_types.UUID `json:"sessionId,omitempty"`
	Status                *PaymentStatus      `json:"status,omitempty"`
	StripePaymentIntentId *string             `json:"stripePaymentIntentId,omitempty"`
	StripePaymentLinkId   *string             `json:"stripePaymentLinkId,omitempty"`
	StripeStatus          *string             `json:"stripeStatus,omitempty"`
	UpdatedAt             *time.Time          `json:"updatedAt,omitempty"`
	UserId                *openapi_types.UUID `json:"userId,omitempty"`
}

// PaymentErrorResponse defines model for PaymentErrorResponse.
type PaymentErrorResponse struct {
	Error     *string                        `json:"error,omitempty"`
	ErrorCode *PaymentErrorResponseErrorCode `json:"errorCode,omitempty"`

	// FallbackOptions Available fallback options like manual payment link
	FallbackOptions *[]string `json:"fallbackOptions,omitempty"`
	Message         *string   `json:"message,omitempty"`
	Retryable       *bool     `json:"retryable,omitempty"`
	Success         *bool     `json:"success,omitempty"`
}

// PaymentErrorResponseErrorCode defines model for PaymentErrorResponse.ErrorCode.
type PaymentErrorResponseErrorCode string

// PaymentLinkResponse defines model for PaymentLinkResponse.
type PaymentLinkResponse struct {
	Amount          *int       `json:"amount,omitempty"`
	ExpiresAt       *time.Time `json:"expiresAt,omitempty"`
	PaymentIntentId *string    `json:"paymentIntentId,omitempty"`
	PaymentLinkUrl  *string    `json:"paymentLinkUrl,omitempty"`
}

// PaymentMethod defines model for PaymentMethod.
type PaymentMethod struct {
	// Brand Card brand (Visa, Mastercard, etc.)
	Brand     *string    `json:"brand,omitempty"`
	CreatedAt *time.Time `json:"createdAt,omitempty"`

	// ExpiryMonth Card expiration month (1-12)
	ExpiryMonth *int `json:"expiryMonth,omitempty"`

	// ExpiryYear Card expiration year
	ExpiryYear *int    `json:"expiryYear,omitempty"`
	Id         *string `json:"id,omitempty"`

	// IsDefault Whether this is the default payment method
	IsDefault *bool `json:"isDefault,omitempty"`

	// Last4 Last 4 digits of card number
	Last4                 *string `json:"last4,omitempty"`
	StripePaymentMethodId *string `json:"stripePaymentMethodId,omitempty"`

	// Type Payment method type
	Type      *PaymentMethodType  `json:"type,omitempty"`
	UpdatedAt *time.Time          `json:"updatedAt,omitempty"`
	UserId    *openapi_types.UUID `json:"userId,omitempty"`
}

// PaymentMethodType Payment method type
type PaymentMethodType string

// PaymentSetupValidationResponse defines model for PaymentSetupValidationResponse.
type PaymentSetupValidationResponse struct {
	// AutoPaymentEnabled Whether auto-payment is enabled for the user
	AutoPaymentEnabled *bool `json:"autoPaymentEnabled,omitempty"`

	// HasDefaultPaymentMethod Whether the user has a default payment method selected
	HasDefaultPaymentMethod *bool `json:"hasDefaultPaymentMethod,omitempty"`

	// HasPaymentMethod Whether the user has any payment methods added
	HasPaymentMethod *bool `json:"hasPaymentMethod,omitempty"`

	// IsSetupComplete Whether the user has a valid payment method for auto-payments
	IsSetupComplete *bool `json:"isSetupComplete,omitempty"`

	// NextStep Next recommended step to complete setup
	NextStep *PaymentSetupValidationResponseNextStep `json:"nextStep,omitempty"`

	// RequiresAction Whether the user needs to take action to enable auto-payment
	RequiresAction *bool `json:"requiresAction,omitempty"`
}

// PaymentSetupValidationResponseNextStep Next recommended step to complete setup
type PaymentSetupValidationResponseNextStep string

// PaymentStatus defines model for PaymentStatus.
type PaymentStatus string

// Plate defines model for Plate.
type Plate struct {
	// Classification Classification number (123)
	Classification *string    `json:"classification,omitempty"`
	CreatedAt      *time.Time `json:"createdAt,omitempty"`

	// Hiragana Hiragana character (あ)
	Hiragana *string             `json:"hiragana,omitempty"`
	Id       *openapi_types.UUID `json:"id,omitempty"`
	IsActive *bool               `json:"isActive,omitempty"`

	// PlateNumber Full generated plate number
	PlateNumber *string    `json:"plateNumber,omitempty"`
	PlateType   *PlateType `json:"plateType,omitempty"`

	// Region Japanese region (品川)
	Region *string `json:"region,omitempty"`

	// SerialNumber Serial number (1234)
	SerialNumber *string             `json:"serialNumber,omitempty"`
	UpdatedAt    *time.Time          `json:"updatedAt,omitempty"`
	UserId       *openapi_types.UUID `json:"userId,omitempty"`
}

// PlateType defines model for PlateType.
type PlateType string

// PriceOverride defines model for PriceOverride.
type PriceOverride struct {
	End          time.Time `json:"end"`
	Name         string    `json:"name"`
	PricePerUnit int       `json:"price_per_unit"`
	Start        time.Time `json:"start"`
	UnitMinutes  int       `json:"unit_minutes"`
}

// PricingRule defines model for PricingRule.
type PricingRule struct {
	Days         []PricingRuleDays `json:"days"`
	End          string            `json:"end"`
	PricePerUnit int               `json:"price_per_unit"`
	Start        string            `json:"start"`
	UnitMinutes  int               `json:"unit_minutes"`
}

// PricingRuleDays defines model for PricingRule.Days.
type PricingRuleDays string

// PricingRules defines model for PricingRules.
type PricingRules struct {
	DailyCap           int              `json:"daily_cap"`
	InitialFreeMinutes int              `json:"initial_free_minutes"`
	LotId              string           `json:"lot_id"`
	NightCaps          *[]NightCap      `json:"night_caps,omitempty"`
	Overrides          *[]PriceOverride `json:"overrides,omitempty"`
	Rules              []PricingRule    `json:"rules"`
}

// RefreshTokenRequest defines model for RefreshTokenRequest.
type RefreshTokenRequest struct {
	// DeviceId Optional device identifier for session tracking
	DeviceId     *string `json:"deviceId,omitempty"`
	RefreshToken string  `json:"refreshToken"`
}

// RefundDetails defines model for RefundDetails.
type RefundDetails struct {
	Amount         *int                `json:"amount,omitempty"`
	CreatedAt      *time.Time          `json:"createdAt,omitempty"`
	Id             *openapi_types.UUID `json:"id,omitempty"`
	ProcessedBy    *openapi_types.UUID `json:"processedBy,omitempty"`
	Reason         *string             `json:"reason,omitempty"`
	Status         *string             `json:"status,omitempty"`
	StripeRefundId *string             `json:"stripeRefundId,omitempty"`
}

// RefundResponse defines model for RefundResponse.
type RefundResponse struct {
	Amount         *int                  `json:"amount,omitempty"`
	CreatedAt      *time.Time            `json:"createdAt,omitempty"`
	Id             *openapi_types.UUID   `json:"id,omitempty"`
	PaymentId      *openapi_types.UUID   `json:"paymentId,omitempty"`
	Reason         *string               `json:"reason,omitempty"`
	Status         *RefundResponseStatus `json:"status,omitempty"`
	StripeRefundId *string               `json:"stripeRefundId,omitempty"`
}

// RefundResponseStatus defines model for RefundResponse.Status.
type RefundResponseStatus string

// RegisterRequest defines model for RegisterRequest.
type RegisterRequest struct {
	Email openapi_types.Email `json:"email"`

	// Name Full name
	Name string `json:"name"`

	// Password Password must be at least 8 characters
	Password string `json:"password"`

	// Phone Phone number
	Phone *string `json:"phone,omitempty"`

	// PreferredLanguage Preferred language code (ja for Japanese, en for English)
	PreferredLanguage RegisterRequestPreferredLanguage `json:"preferredLanguage"`

	// SetupPaymentMethod Whether to setup payment method during registration
	SetupPaymentMethod *bool `json:"setupPaymentMethod,omitempty"`

	// Username Unique username for login
	Username string `json:"username"`
}

// RegisterRequestPreferredLanguage Preferred language code (ja for Japanese, en for English)
type RegisterRequestPreferredLanguage string

// ResendVerificationRequest defines model for ResendVerificationRequest.
type ResendVerificationRequest struct {
	// Email Email address to resend verification to
	Email openapi_types.Email `json:"email"`
}

// ResetPasswordRequest defines model for ResetPasswordRequest.
type ResetPasswordRequest struct {
	// Password New password
	Password string `json:"password"`

	// Token Reset token sent to user's email
	Token string `json:"token"`
}

// ResetPasswordResponse defines model for ResetPasswordResponse.
type ResetPasswordResponse struct {
	Message     *string `json:"message,omitempty"`
	RedirectUrl *string `json:"redirectUrl,omitempty"`
}

// ScenarioResult defines model for ScenarioResult.
type ScenarioResult struct {
	AppliedRules    *[]string     `json:"appliedRules,omitempty"`
	BillableMinutes *int          `json:"billableMinutes,omitempty"`
	Breakdown       *FeeBreakdown `json:"breakdown,omitempty"`
	Fee             *int          `json:"fee,omitempty"`
	FreeMinutesUsed *int          `json:"freeMinutesUsed,omitempty"`
	Name            *string       `json:"name,omitempty"`
	TotalMinutes    *int          `json:"totalMinutes,omitempty"`
}

// Session defines model for Session.
type Session struct {
	// Amount Amount in JPY
	Amount              *int                `json:"amount,omitempty"`
	CreatedAt           *time.Time          `json:"createdAt,omitempty"`
	DetectionConfidence *float64            `json:"detectionConfidence,omitempty"`
	DiscountAmount      *int                `json:"discountAmount,omitempty"`
	DurationMinutes     *int                `json:"durationMinutes,omitempty"`
	EntryImageUrl       *string             `json:"entryImageUrl,omitempty"`
	EntryTime           *time.Time          `json:"entryTime,omitempty"`
	ErrorMessage        *string             `json:"errorMessage,omitempty"`
	ExitImageUrl        *string             `json:"exitImageUrl,omitempty"`
	ExitTime            *time.Time          `json:"exitTime,omitempty"`
	Id                  *openapi_types.UUID `json:"id,omitempty"`
	IsPaid              *bool               `json:"isPaid,omitempty"`
	ManualOverride      *bool               `json:"manualOverride,omitempty"`
	ParkingLot          *ParkingLot         `json:"parkingLot,omitempty"`
	ParkingLotId        *openapi_types.UUID `json:"parkingLotId,omitempty"`
	Payment             *Payment            `json:"payment,omitempty"`
	Plate               *Plate              `json:"plate,omitempty"`
	PlateId             *openapi_types.UUID `json:"plateId,omitempty"`
	Status              *SessionStatus      `json:"status,omitempty"`
	UpdatedAt           *time.Time          `json:"updatedAt,omitempty"`
	UserId              *openapi_types.UUID `json:"userId,omitempty"`
}

// SessionResponse defines model for SessionResponse.
type SessionResponse struct {
	// CreatedAt When the session was created
	CreatedAt *time.Time `json:"createdAt,omitempty"`

	// DeviceId Device identifier
	DeviceId *string `json:"deviceId,omitempty"`

	// DeviceName Human-readable device name
	DeviceName *string `json:"deviceName,omitempty"`

	// DeviceType Device type (mobile, desktop, tablet)
	DeviceType *string             `json:"deviceType,omitempty"`
	Id         *openapi_types.UUID `json:"id,omitempty"`

	// IpAddress IP address of the session
	IpAddress *string `json:"ipAddress,omitempty"`

	// IsCurrent Whether this is the current session
	IsCurrent *bool `json:"isCurrent,omitempty"`

	// LastUsedAt Last time this session was used
	LastUsedAt *time.Time `json:"lastUsedAt,omitempty"`
}

// SessionStatus defines model for SessionStatus.
type SessionStatus string

// SessionsResponse defines model for SessionsResponse.
type SessionsResponse struct {
	Sessions *[]SessionResponse `json:"sessions,omitempty"`
}

// SetupIntentResponse defines model for SetupIntentResponse.
type SetupIntentResponse struct {
	// ClientSecret Stripe setup intent client secret
	ClientSecret *string `json:"clientSecret,omitempty"`

	// CustomerId Stripe customer ID
	CustomerId *string `json:"customerId,omitempty"`

	// EphemeralKey Ephemeral key for mobile SDKs
	EphemeralKey *string `json:"ephemeralKey,omitempty"`

	// PublishableKey Stripe publishable key
	PublishableKey *string `json:"publishableKey,omitempty"`

	// RedirectUrl URL for redirect flow if needed
	RedirectUrl *string `json:"redirectUrl,omitempty"`

	// SetupIntentId Stripe setup intent ID
	SetupIntentId *string `json:"setupIntentId,omitempty"`
}

// StripeSetupCallbackRequest defines model for StripeSetupCallbackRequest.
type StripeSetupCallbackRequest struct {
	// IsDefault Set as default payment method
	IsDefault *bool `json:"isDefault,omitempty"`

	// PaymentMethodId Stripe payment method ID
	PaymentMethodId string `json:"paymentMethodId"`

	// SetupIntentId Stripe setup intent ID
	SetupIntentId string `json:"setupIntentId"`
}

// UpdateParkingLotRequest defines model for UpdateParkingLotRequest.
type UpdateParkingLotRequest struct {
	Address       *string    `json:"address,omitempty"`
	CloseTime     *string    `json:"closeTime,omitempty"`
	ContactPhone  *string    `json:"contactPhone,omitempty"`
	DailyMaxRate  *int       `json:"dailyMaxRate,omitempty"`
	Features      *[]string  `json:"features,omitempty"`
	FreeMinutes   *int       `json:"freeMinutes,omitempty"`
	HeightLimitCm *int       `json:"heightLimitCm,omitempty"`
	HourlyRate    *int       `json:"hourlyRate,omitempty"`
	Is24h         *bool      `json:"is24h,omitempty"`
	Latitude      *float64   `json:"latitude,omitempty"`
	Longitude     *float64   `json:"longitude,omitempty"`
	Name          *string    `json:"name,omitempty"`
	OpenTime      *string    `json:"openTime,omitempty"`
	OperatorName  *string    `json:"operatorName,omitempty"`
	Status        *LotStatus `json:"status,omitempty"`
	TotalSpots    *int       `json:"totalSpots,omitempty"`
}

// UpdatePricingConfigRequest defines model for UpdatePricingConfigRequest.
type UpdatePricingConfigRequest struct {
	ConfigName   *string       `json:"configName,omitempty"`
	PricingRules *PricingRules `json:"pricingRules,omitempty"`
}

// UpdateUserRequest defines model for UpdateUserRequest.
type UpdateUserRequest struct {
	// AutoPaymentEnabled Enable/disable automatic payment processing
	AutoPaymentEnabled *bool `json:"autoPaymentEnabled,omitempty"`

	// DefaultPaymentMethodId Stripe payment method ID for automatic payments
	DefaultPaymentMethodId *string `json:"defaultPaymentMethodId,omitempty"`

	// Email Email address (will require re-verification if changed)
	Email *openapi_types.Email `json:"email,omitempty"`

	// Name Full name
	Name *string `json:"name,omitempty"`

	// NotifyEmail Enable/disable email notifications
	NotifyEmail *bool `json:"notifyEmail,omitempty"`

	// NotifyPush Enable/disable push notifications
	NotifyPush *bool `json:"notifyPush,omitempty"`

	// Phone Phone number
	Phone *string `json:"phone,omitempty"`

	// PreferredLanguage Preferred language code (ja for Japanese, en for English)
	PreferredLanguage *UpdateUserRequestPreferredLanguage `json:"preferredLanguage,omitempty"`

	// Username Unique username for login
	Username *string `json:"username,omitempty"`
}

// UpdateUserRequestPreferredLanguage Preferred language code (ja for Japanese, en for English)
type UpdateUserRequestPreferredLanguage string

// User defines model for User.
type User struct {
	// AutoPaymentEnabled Enable automatic payment processing on parking exit
	AutoPaymentEnabled *bool      `json:"autoPaymentEnabled,omitempty"`
	CreatedAt          *time.Time `json:"createdAt,omitempty"`

	// DefaultPaymentMethodId Stripe payment method ID for automatic payments
	DefaultPaymentMethodId *string              `json:"defaultPaymentMethodId,omitempty"`
	Email                  *openapi_types.Email `json:"email,omitempty"`
	EmailVerified          *bool                `json:"emailVerified,omitempty"`
	Id                     *openapi_types.UUID  `json:"id,omitempty"`
	LastLoginAt            *time.Time           `json:"lastLoginAt,omitempty"`

	// Name Full name
	Name        *string `json:"name,omitempty"`
	NotifyEmail *bool   `json:"notifyEmail,omitempty"`
	NotifyPush  *bool   `json:"notifyPush,omitempty"`

	// Phone Phone number
	Phone *string `json:"phone,omitempty"`

	// PreferredLanguage Preferred language code
	PreferredLanguage *UserPreferredLanguage `json:"preferredLanguage,omitempty"`
	Role              *UserRole              `json:"role,omitempty"`
	Status            *UserStatus            `json:"status,omitempty"`
	StripeCustomerId  *string                `json:"stripeCustomerId,omitempty"`
	UpdatedAt         *time.Time             `json:"updatedAt,omitempty"`

	// Username Unique username
	Username *string `json:"username,omitempty"`
}

// UserPreferredLanguage Preferred language code
type UserPreferredLanguage string

// UserRole defines model for UserRole.
type UserRole string

// UserStatus defines model for UserStatus.
type UserStatus string

// ValidationResponse defines model for ValidationResponse.
type ValidationResponse struct {
	Errors   *[]string `json:"errors,omitempty"`
	Valid    *bool     `json:"valid,omitempty"`
	Warnings *[]string `json:"warnings,omitempty"`
}

// BadRequest defines model for BadRequest.
type BadRequest = ErrorResponse

// Conflict defines model for Conflict.
type Conflict = ErrorResponse

// Forbidden defines model for Forbidden.
type Forbidden = ErrorResponse

// InternalServerError defines model for InternalServerError.
type InternalServerError = ErrorResponse

// NotFound defines model for NotFound.
type NotFound = ErrorResponse

// Unauthorized defines model for Unauthorized.
type Unauthorized = ErrorResponse

// GetAdminParkingLotsParams defines parameters for GetAdminParkingLots.
type GetAdminParkingLotsParams struct {
	Limit  *int       `form:"limit,omitempty" json:"limit,omitempty"`
	Offset *int       `form:"offset,omitempty" json:"offset,omitempty"`
	Status *LotStatus `form:"status,omitempty" json:"status,omitempty"`

	// Search Search by name or address
	Search *string `form:"search,omitempty" json:"search,omitempty"`
}

// GetAdminParkingLotsLotIdPricingConfigsParams defines parameters for GetAdminParkingLotsLotIdPricingConfigs.
type GetAdminParkingLotsLotIdPricingConfigsParams struct {
	IncludeInactive *bool `form:"includeInactive,omitempty" json:"includeInactive,omitempty"`
}

// GetAdminPaymentsParams defines parameters for GetAdminPayments.
type GetAdminPaymentsParams struct {
	Limit     *int                `form:"limit,omitempty" json:"limit,omitempty"`
	Offset    *int                `form:"offset,omitempty" json:"offset,omitempty"`
	Status    *PaymentStatus      `form:"status,omitempty" json:"status,omitempty"`
	UserId    *openapi_types.UUID `form:"userId,omitempty" json:"userId,omitempty"`
	SessionId *openapi_types.UUID `form:"sessionId,omitempty" json:"sessionId,omitempty"`
	DateFrom  *openapi_types.Date `form:"dateFrom,omitempty" json:"dateFrom,omitempty"`
	DateTo    *openapi_types.Date `form:"dateTo,omitempty" json:"dateTo,omitempty"`
	MinAmount *int                `form:"minAmount,omitempty" json:"minAmount,omitempty"`
	MaxAmount *int                `form:"maxAmount,omitempty" json:"maxAmount,omitempty"`
}

// PostAdminPaymentsPaymentIdRefundJSONBody defines parameters for PostAdminPaymentsPaymentIdRefund.
type PostAdminPaymentsPaymentIdRefundJSONBody struct {
	// Amount Amount to refund in JPY (if partial)
	Amount *int `json:"amount,omitempty"`

	// Reason Reason for refund
	Reason string `json:"reason"`
}

// GetAdminPlatesParams defines parameters for GetAdminPlates.
type GetAdminPlatesParams struct {
	Limit  *int `form:"limit,omitempty" json:"limit,omitempty"`
	Offset *int `form:"offset,omitempty" json:"offset,omitempty"`

	// UserId Filter by user ID
	UserId *openapi_types.UUID `form:"userId,omitempty" json:"userId,omitempty"`

	// PlateNumber Search by plate number
	PlateNumber *string    `form:"plateNumber,omitempty" json:"plateNumber,omitempty"`
	PlateType   *PlateType `form:"plateType,omitempty" json:"plateType,omitempty"`
	IsActive    *bool      `form:"isActive,omitempty" json:"isActive,omitempty"`
}

// GetAdminSessionsParams defines parameters for GetAdminSessions.
type GetAdminSessionsParams struct {
	Limit        *int                `form:"limit,omitempty" json:"limit,omitempty"`
	Offset       *int                `form:"offset,omitempty" json:"offset,omitempty"`
	Status       *SessionStatus      `form:"status,omitempty" json:"status,omitempty"`
	UserId       *openapi_types.UUID `form:"userId,omitempty" json:"userId,omitempty"`
	ParkingLotId *openapi_types.UUID `form:"parkingLotId,omitempty" json:"parkingLotId,omitempty"`
	PlateNumber  *string             `form:"plateNumber,omitempty" json:"plateNumber,omitempty"`
	DateFrom     *openapi_types.Date `form:"dateFrom,omitempty" json:"dateFrom,omitempty"`
	DateTo       *openapi_types.Date `form:"dateTo,omitempty" json:"dateTo,omitempty"`
	IsPaid       *bool               `form:"isPaid,omitempty" json:"isPaid,omitempty"`
}

// PostAdminSessionsSessionIdCancelJSONBody defines parameters for PostAdminSessionsSessionIdCancel.
type PostAdminSessionsSessionIdCancelJSONBody struct {
	// Reason Reason for cancellation
	Reason string `json:"reason"`
}

// PostAdminSessionsSessionIdCompleteJSONBody defines parameters for PostAdminSessionsSessionIdComplete.
type PostAdminSessionsSessionIdCompleteJSONBody struct {
	// Amount Override calculated amount
	Amount   *int       `json:"amount,omitempty"`
	ExitTime *time.Time `json:"exitTime,omitempty"`

	// Reason Reason for manual completion
	Reason *string `json:"reason,omitempty"`
}

// GetAdminUsersParams defines parameters for GetAdminUsers.
type GetAdminUsersParams struct {
	Limit  *int        `form:"limit,omitempty" json:"limit,omitempty"`
	Offset *int        `form:"offset,omitempty" json:"offset,omitempty"`
	Status *UserStatus `form:"status,omitempty" json:"status,omitempty"`
	Role   *UserRole   `form:"role,omitempty" json:"role,omitempty"`

	// Search Search by username, email, or name
	Search        *string `form:"search,omitempty" json:"search,omitempty"`
	EmailVerified *bool   `form:"emailVerified,omitempty" json:"emailVerified,omitempty"`
}

// PostAdminUsersUserIdSuspendJSONBody defines parameters for PostAdminUsersUserIdSuspend.
type PostAdminUsersUserIdSuspendJSONBody struct {
	// Reason Reason for suspension
	Reason *string `json:"reason,omitempty"`
}

// GetAuthVerifyEmailParams defines parameters for GetAuthVerifyEmail.
type GetAuthVerifyEmailParams struct {
	// Token Email verification token from magic link
	Token string `form:"token" json:"token"`
}

// GetBookingsParams defines parameters for GetBookings.
type GetBookingsParams struct {
	Status   *BookingStatus `form:"status,omitempty" json:"status,omitempty"`
	Upcoming *bool          `form:"upcoming,omitempty" json:"upcoming,omitempty"`
}

// GetNotificationsParams defines parameters for GetNotifications.
type GetNotificationsParams struct {
	Unread *bool `form:"unread,omitempty" json:"unread,omitempty"`
	Limit  *int  `form:"limit,omitempty" json:"limit,omitempty"`
}

// GetParkingLotsParams defines parameters for GetParkingLots.
type GetParkingLotsParams struct {
	// Lat Latitude for location-based search
	Lat *float64 `form:"lat,omitempty" json:"lat,omitempty"`

	// Lng Longitude for location-based search
	Lng *float64 `form:"lng,omitempty" json:"lng,omitempty"`

	// Radius Search radius in meters
	Radius *int                      `form:"radius,omitempty" json:"radius,omitempty"`
	Sort   *GetParkingLotsParamsSort `form:"sort,omitempty" json:"sort,omitempty"`

	// Features Filter by features
	Features *[]string `form:"features,omitempty" json:"features,omitempty"`
	Limit    *int      `form:"limit,omitempty" json:"limit,omitempty"`
	Offset   *int      `form:"offset,omitempty" json:"offset,omitempty"`
}

// GetParkingLotsParamsSort defines parameters for GetParkingLots.
type GetParkingLotsParamsSort string

// GetPaymentsParams defines parameters for GetPayments.
type GetPaymentsParams struct {
	Limit  *int `form:"limit,omitempty" json:"limit,omitempty"`
	Offset *int `form:"offset,omitempty" json:"offset,omitempty"`
}

// PostPaymentsWebhooksStripeJSONBody defines parameters for PostPaymentsWebhooksStripe.
type PostPaymentsWebhooksStripeJSONBody = map[string]interface{}

// GetSessionsParams defines parameters for GetSessions.
type GetSessionsParams struct {
	Status *SessionStatus `form:"status,omitempty" json:"status,omitempty"`
	Limit  *int           `form:"limit,omitempty" json:"limit,omitempty"`
	Offset *int           `form:"offset,omitempty" json:"offset,omitempty"`
}

// PostAdminParkingLotsJSONRequestBody defines body for PostAdminParkingLots for application/json ContentType.
type PostAdminParkingLotsJSONRequestBody = CreateParkingLotRequest

// PutAdminParkingLotsLotIdJSONRequestBody defines body for PutAdminParkingLotsLotId for application/json ContentType.
type PutAdminParkingLotsLotIdJSONRequestBody = UpdateParkingLotRequest

// PostAdminParkingLotsLotIdPricingConfigsJSONRequestBody defines body for PostAdminParkingLotsLotIdPricingConfigs for application/json ContentType.
type PostAdminParkingLotsLotIdPricingConfigsJSONRequestBody = CreatePricingConfigRequest

// PutAdminParkingLotsLotIdPricingConfigsConfigIdJSONRequestBody defines body for PutAdminParkingLotsLotIdPricingConfigsConfigId for application/json ContentType.
type PutAdminParkingLotsLotIdPricingConfigsConfigIdJSONRequestBody = UpdatePricingConfigRequest

// PostAdminParkingLotsLotIdPricingConfigsConfigIdActivateJSONRequestBody defines body for PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate for application/json ContentType.
type PostAdminParkingLotsLotIdPricingConfigsConfigIdActivateJSONRequestBody = ActivatePricingConfigRequest

// PostAdminPaymentsPaymentIdRefundJSONRequestBody defines body for PostAdminPaymentsPaymentIdRefund for application/json ContentType.
type PostAdminPaymentsPaymentIdRefundJSONRequestBody PostAdminPaymentsPaymentIdRefundJSONBody

// PostAdminPricingConfigsCalculatePreviewJSONRequestBody defines body for PostAdminPricingConfigsCalculatePreview for application/json ContentType.
type PostAdminPricingConfigsCalculatePreviewJSONRequestBody = FeeCalculationPreviewRequest

// PostAdminPricingConfigsValidateJSONRequestBody defines body for PostAdminPricingConfigsValidate for application/json ContentType.
type PostAdminPricingConfigsValidateJSONRequestBody = PricingRules

// PutAdminSessionsSessionIdJSONRequestBody defines body for PutAdminSessionsSessionId for application/json ContentType.
type PutAdminSessionsSessionIdJSONRequestBody = AdminUpdateSessionRequest

// PostAdminSessionsSessionIdCancelJSONRequestBody defines body for PostAdminSessionsSessionIdCancel for application/json ContentType.
type PostAdminSessionsSessionIdCancelJSONRequestBody PostAdminSessionsSessionIdCancelJSONBody

// PostAdminSessionsSessionIdCompleteJSONRequestBody defines body for PostAdminSessionsSessionIdComplete for application/json ContentType.
type PostAdminSessionsSessionIdCompleteJSONRequestBody PostAdminSessionsSessionIdCompleteJSONBody

// PutAdminUsersUserIdJSONRequestBody defines body for PutAdminUsersUserId for application/json ContentType.
type PutAdminUsersUserIdJSONRequestBody = AdminUpdateUserRequest

// PostAdminUsersUserIdSuspendJSONRequestBody defines body for PostAdminUsersUserIdSuspend for application/json ContentType.
type PostAdminUsersUserIdSuspendJSONRequestBody PostAdminUsersUserIdSuspendJSONBody

// PostAuthChangePasswordJSONRequestBody defines body for PostAuthChangePassword for application/json ContentType.
type PostAuthChangePasswordJSONRequestBody = ChangePasswordRequest

// PostAuthForgotPasswordJSONRequestBody defines body for PostAuthForgotPassword for application/json ContentType.
type PostAuthForgotPasswordJSONRequestBody = ForgotPasswordRequest

// PostAuthLoginJSONRequestBody defines body for PostAuthLogin for application/json ContentType.
type PostAuthLoginJSONRequestBody = LoginRequest

// PostAuthRefreshJSONRequestBody defines body for PostAuthRefresh for application/json ContentType.
type PostAuthRefreshJSONRequestBody = RefreshTokenRequest

// PostAuthRegisterJSONRequestBody defines body for PostAuthRegister for application/json ContentType.
type PostAuthRegisterJSONRequestBody = RegisterRequest

// PostAuthResendVerificationJSONRequestBody defines body for PostAuthResendVerification for application/json ContentType.
type PostAuthResendVerificationJSONRequestBody = ResendVerificationRequest

// PostAuthResetPasswordJSONRequestBody defines body for PostAuthResetPassword for application/json ContentType.
type PostAuthResetPasswordJSONRequestBody = ResetPasswordRequest

// PostBookingsJSONRequestBody defines body for PostBookings for application/json ContentType.
type PostBookingsJSONRequestBody = CreateBookingRequest

// PostHardwareDetectionJSONRequestBody defines body for PostHardwareDetection for application/json ContentType.
type PostHardwareDetectionJSONRequestBody = HardwareDetectionRequest

// PostPaymentMethodsStripeCallbackJSONRequestBody defines body for PostPaymentMethodsStripeCallback for application/json ContentType.
type PostPaymentMethodsStripeCallbackJSONRequestBody = StripeSetupCallbackRequest

// PostPaymentsWebhooksStripeJSONRequestBody defines body for PostPaymentsWebhooksStripe for application/json ContentType.
type PostPaymentsWebhooksStripeJSONRequestBody = PostPaymentsWebhooksStripeJSONBody

// PostPaymentsSessionIdProcessAutoPaymentJSONRequestBody defines body for PostPaymentsSessionIdProcessAutoPayment for application/json ContentType.
type PostPaymentsSessionIdProcessAutoPaymentJSONRequestBody = AutoPaymentRequest

// PostUsersPlatesJSONRequestBody defines body for PostUsersPlates for application/json ContentType.
type PostUsersPlatesJSONRequestBody = CreatePlateRequest

// PutUsersProfileJSONRequestBody defines body for PutUsersProfile for application/json ContentType.
type PutUsersProfileJSONRequestBody = UpdateUserRequest
