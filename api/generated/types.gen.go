// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"bytes"
	"compress/gzip"
	"encoding/base64"
	"fmt"
	"net/url"
	"path"
	"strings"

	"github.com/getkin/kin-openapi/openapi3"
)

// Base64 encoded, gzipped, json marshaled Swagger object
var swaggerSpec = []string{

	"H4sIAAAAAAAC/+x9W28cTXbYXylMFjA/ZEZDSrLxLV8SipQs7VJagpR2s5FpothdM1PLnu7+qqopzcoE",
	"MoRhOHEMIzHyEARGECAIglyBAEEeAiQ/ZrD/w6hbd3V3VXf13KVv38jpup5bnTrn1DlfekEyTZMYxYz2",
	"jr/0CKJpElMk/nkBw0v0Q4Yo4/8FScxQLP6EaRrhADKcxMPf0CTmv9FggqaQ//UTgka9494/GBZDD+VX",
	"OnxJSEIu1SS9h4eHfi9ENCA45YP1jvmcQE/60O+dJvEowsEWF5DP+NDvvUrILQ5DFG9v+mLKh37vTcwQ",
	"iWF0hcg9IqLv9laiJwdydiCnf+j33iXsVZLF4faW8i5hQE750O99iGHGJgnBv0VbXEJpVv5Z9eQDnwQM",
	"30OGLggOcDzmFITHBuekJEkRYVhyFRqNEO+AXpFkyn8YJWQKWe+4F0KGBgxPUa/fY7MU9Y57lBEcj/m2",
	"824fYoYj334P/R5BP2SYcFh9rMx9nTdPbn+DJMmfhFMcX0Byh+PxecLOEIM4EuuGUfSLUe/4YzMgi669",
	"h3516/AeEThGV4hSnMRnGYESul/0OnDM0BgJKgsEFFUT0RszNKVtiCzml2jgQ6nBISFwxv9PEbx7nWSk",
	"PGoN4tVuLGEwukT3KM6Qfc2ihdodtTV5qEHcAwdX2XQKyaxOSTAMCaL2xQcEQYbCE+ZPYUFGCIrZL4Ig",
	"S2EczOx7xGFpxCzDoW2wGE6RdWGUQZa1opFvWzYUYA3hzAPwacI8oV6DMc15vwblCE8xs08bqQm9KNOB",
	"VQuhJaMRRaxhpx03OZuieBlOFv0sbBximmYM+W/9THbQS7BsmaBRFof+I16K9g0DUsmFXkhRHGtgJKOI",
	"eHX9QBHJ+3XibgFcN2tPkyx2kMASrO3JtGlOne9c7JvKhb9FbJKE9hYRZOhdNr2VIKwLAAntN35L8hMX",
	"Gpy5yOADpEj9/EYoCG/sy+Wo9lwLb+oATBv3LSdfmgSBQkRn+VOiO9cp1yB7TpyU6YYBp4ju8of3skif",
	"CFL2gaLQn/oJCviejXPZC2CqgxNIFwYGltAFtiFlOAidMmZzcgRToRKbjHKbJBGCApjdEdgmUsT39+JX",
	"D5oSDf0x9HIKcbQT0cEXuwHBIYbtKDZMQuoiNJyb03eArlKhYMoqOEYIvSAI3oXJp9Zj/5XZtnTw+d8v",
	"DCHseTzl0Pci061IiIrqs2k9JFT3ubc41gpkfVwUMzJ7jyWzeF6OP2PWrYe3JLuAOLTLMR9tqU0X8tJu",
	"NJJy7WbDgkfLxLWLHtr1CHao56uLnw8ppw41stNU00T9IaYB/3rS0KY7WS5FEC275LLBvcWMJUo8vYzh",
	"bYQcxI6cp6D48ktE8Ai7OjuNAekkie1fSBK1ikmxMd7OG3BCTJbYKO7IG3wE49BaDphLSM6V4O8p7Lhi",
	"dp6McdxlYUvgtqMGkp+GNaPBFmhEXiRPM8qSqRa6dUvhWq4DyoiFXNIkE9zciWqWpfB13hm+PsrdZ9mz",
	"/kO54cLPF9nxsC4polWWte4sYxP3nmAQIErfJ3cotpPQ5xQTRN/EjUaSK8Sy9DL3g3yp+HZ+NUFsgghQ",
	"rQHlzQGmIHed9C2kSdCIIDpxr00M8yZ3TjUf6HnTwgnlp/5zgDshq08jDw2n4gWFUZBxuRsCpeaCEUIA",
	"x+BnF78uwGHeBvIerxCqD/gKIaCb4CQGt/n1q19Z0i2kSA1Rn2WEYxg5vtpgMEYxIpChSxQgnKqNjmAW",
	"sd4xIxmquvn+WLUHMA4BRXEIiOwJ+Nk+hQwHMIpmVoLgzd8lDI+UC7J1sis+viY6jtoICdjE5iC2mTKK",
	"zuTAF1VzbOOEHygCnKb+gALVLp9+Kkeoz1bxICqCqeL7uo3+nAzedMGEJHxBYGw/dPnXc0jZc/tX4cyS",
	"TqwcJr0S8Rq3vPg+wUHTRW2KKIVjsX70GXJM9Y57anMgJQmXUigENBPyapSVaMS0nuNOZ6fCzhtf871B",
	"DNocZrWG4pR9INEKV9IVDO4KRrbz3sbBL5KEix+LKgLjAEVSnDjlhWrU0RG6hJITh90uepMkI9HsUhmD",
	"lnazLm+40v/50lYnu5Vo7e/hIRu5JSvKMf3Ijf6LpTVsr4020PZVvh0UZ1MuZUXwA5kKzSMnYf63PCTE",
	"33FyQyfJJ0P0Fqs6ncB4jC4gpZ8SEjpPf+Xx1+0saoBsAFLdwqZGo0/uAd6hT3lncDDFMZ5mU/A9CCaQ",
	"wIAhQr/r9XtTHJ+jeMwmvePv20JYqmsuL8B2Dp0KdlawdofldOXg5VhoU0xRgZKeq7JMc+BCaLmBVggN",
	"twbZFIQSJRTpXdS/JjGDAbtw2glCiKPZW/jZLSVHCLKMoI6BPCOCUKMFeoLweMLO+X3qdOpo0ia/6dPn",
	"E5f/i2GWhRXUJtltZOA1lrqICDKJx13aO6+1SYpiJzI4SiFLiNOU3RpiY5KfWEM/pw1jz+Z+SoM2EGEk",
	"FHiXDIsgpSWdewo/a2nyzHbyYgLHMIaVpkf9dfkVCRrXV3J0aONyRDCMCrXTaP+8jcPVLP0qAIwNViZo",
	"gLBX4KKMyNMEUtqbbXOpHPQyi1C7Omm2rUn8Yt7KqLYd2S5G7uuHVa8/s96NgNIOPJR8Y+r1KMFniKGA",
	"o/cME/mHqS0Ih1lPuhus6kDevcnQYh31RunCavQbgoKEhOJ/5cy5UWCRGokaxraIFgNBo8UBHIwSAvgK",
	"gJxBqA11qWug03Vem1Gn5RW858Ion1y7KvkKpvKw8F2Fp1fYdj+u+wzt9okQcPIDoi1Qsr+/cqxVR6Is",
	"R/Vt2neM7nGI4sBEbrEWz+sSQZAmccu91/KJ323Vdq0MbQPPy8K6LUWzU7AybUIs41oMAO6NEYBoCUYk",
	"mYIpHOMARDi+a1UG5fg2YVkOe7dI/NChmRU4r42J9DOFJiNKXb3AU0QZnKYdFN7a1K8qISAdDItC1zyF",
	"6UmaRmVvREefb5OFst+LuWLZNk1yjwjBIepg53yF0GlhXb0g6B6jT06CW/5o7vdogGJIcNI5JP9KdbS6",
	"BUp3F3NGc8Jr/427KLr76vWyLxHNIubn03iVkHHCWq/fuWcsp3f5S+srDtHq2mPiTmqP7gYIoogBHFNG",
	"MnnOAcr1IJaAWZIR4F5lbUGvIQk/QYIMDcQBjGfndQn4bBDiMWagrOIW510KGUOEt/zTj4eDn15/efbw",
	"E5vYPz2y6BuYzcA9jDIEDn4GUxgjioBUqkGE7xD43d/Of/d//k6YJ1rU+NeW4Y9AhPjiwGuljYOEAEim",
	"swFBUstR3w9e9l/3f95/23/f/3V1svIOF/N/s5j/28X83y3mf7eY//vF/D8s5v9xMf9Pi/l/Xsz/y2L+",
	"Xxfz/7aY//fF/H8s5v9zMf9fi/n/Xsz/72L+/xbz/794/GeLx/ni8XHx+OeLx79YPP7zxeO/WDz+1eLx",
	"rxfzx8X8zxfzv1jM/3Ix/6vF/K8X879ZzP/VYv63i8d/vXj8m8Xjv1w8/uXL1z9/+/7X11b4vjmrA+C5",
	"wp28gjTg7LkdZ0Lrzw/88tg5NYGiFaBBQvg5Ub8iT+FnPOVK7ZEwNcm/Dy1XZzyFY6QM4+UZfyH+gBEQ",
	"TcCHy3N5Cod6JQ0WImyxi/2Rgo7WNqOEAb4PhkfYDqY/soOpUFvKExweA2HqQSFnWz4NOBAa/Xd9cHQM",
	"IjRigE1QaQEHXLX9ThiGxBXgsH90bdNvmWW+9/r8BsmoBJUljFeMi/3To16fCwXBXoLETIDmG7dJQREJ",
	"4BQ0Bphru/igvPKcWYWUA4UJw4Jel9WTD9NgM61st4L3Bktm8SrKuKdBGWrNyZzjKIacZZTtLbTexN5K",
	"6e91PJQ3prsA3aL5FlFxRvpcKt4pBcnm7xE/Whi4FDsalg+1w++PDw/L/HTw8XBwdP2PBFv92dOPh4Nn",
	"198dfzwc/OG1+OknTntseeSnT9cwcoUU5DRyG32xYxsZVJ3M2wvrvyoHJxlGTY+bdwdLeSe3J9dNuuy2",
	"26WYYRahBrNys/ZookrbB7ftZaqtwRAeHDVIugkEyG9MDxO/h1AGZzefIIn58NL8hm5ghASZ3kqfyg1B",
	"UxyHJRNjsfaLkotySefBiuzb6mpY/rnsyT3EEbzFEWYzfx9sqZfF1VEWu2+lAgNEKyDiUxoicUJMGbTr",
	"TeqLsGkhhogyaVEESTDhen8WMWHU8vAzrMX3UgkSIqjQSJTRzbrFzg6ayjHWAkFfech1wY7733O/0Ip8",
	"1upFWur1d5PjaQlxapORDs6sCyz5NUINCxLv25RR3FuaJPrVvaZYD4x2f/Vey47Q4uRZi5yUXV7MvHhq",
	"q9kx1vSgsbt+s4L9bc3kvoXEB+XIo40lO6iaGS0RHlt4V+aO6bb4x4sVGVNdW/eWe5T84nelhbo5XneF",
	"4MpltSXPeMwRxFFG0KXbW+PLt62BnUtGZK4WY0kQI7NTtw/BM3mF8UB9z5IqlFqe4/iusd2V2/W27buS",
	"fr3V7Bdzu7jEl1PlNtOXLBzTbDTCAeYXLJnnRHLYTYiCCMfKvZ5y6XDDf5e+dH0ly0OzVZwxv3HJFVxb",
	"eSeKbmFwJy2WFiX7RKswQDcFiWwr7d5TGGfCDy6DH5SH0V/DbbIECMLnc9sPUpvpaAQj6mk7MuhtuYBz",
	"9ZpkmQjtJm5Ii3XZJULDZorI/oozU4vuahQFCYH4Bg5+iSnsg7eQMkQ4VfUBYsGT79YV8cxhNXubxGzi",
	"WIZoIT02U94MHBwNjp7aQybkaL9GkLQPNuOt3Nc2i/J2ponJ9fKHTTAFmAqLuPfLCKnpP6+Pyg9O8BwI",
	"Cz8FyQhw6DdFaZjSsjFsSJucyvNdlOOURKN+EUssBUoKZymcWUXGjoSsePT0SxjhELbEJlmf1doxydsO",
	"NOowBUh2AKOECOyKJ1U2XE4gdb+psdOMHA1MIAXQQTaAoggFzPGEbALpUpPFs8o0FMAwdEyCqQD0qTLs",
	"eW/onmOmuh0ORhPE1DpljD6zK4ZSWzz4ZwYICpLpFMUhCgFlKAUs0c+fkHx8Z5AvDMP6SUgRu1EAr3+U",
	"KL/hy9Qfe5rIb2rHbIysPKG0dXHvswWL1YAWIxRSvhMG75CKD+P/ytWUgNbrdJzVPT4pikNtjtUaQeVx",
	"ANegxR8ysZrDEXShH3W0hdRWBLLNHQ8Ojp4+qziwn63nqDEjdssryZ3r+XsCcLCYP9b96Bu6+DeF6r3K",
	"ogiM83i9tlC9VWONy5NXIxoO/KMZqkHJ1TeLhjtf4Px5ZdDn+3DCmMDUbBPzcSLJKFNEAiz+IShm4o8p",
	"jjCDxH5EXhAcoF+ouCzr45F1ZEwQzpUUkZsslpaXZmdn7pT0hGiM2c20sL3ngx/1PeP5y+7J0ni11VvN",
	"GIUtqw7DEM7KNiWNt7cijuB9xlfwKyHU3k+yXr/3iuBev3cF+YquMnvQcfVyohDVwcz9Z3/69Pnx4eFP",
	"XHa8pRC2opl9BUwKKK8Vk9SGShzNbrwc9jjGDMPoZkQQsm7J2itK2I1D2xcRlnxufwNlHnNgM08qnu9g",
	"7iyJClvaEg007+E0z7RFTiqwOKDaN/CiV2HD7aWR2MAZRROiexyoYGhHqJRsYoQ1AelrFJYqwAgM7qTi",
	"YlG+GlMr1F7CGK0dGzKSxe5LrlX9aNzTMbJK6LoEgHfkumy+5Jv9DSan7RIU4gWuukItLFAoNHXo6/7a",
	"wDrGlDXk4vKOBy50CIvKqc5qQyN7an+f5QxfywOBpxll4BYByECEIGWlh7tt73aNDFCV4fnPhSZcWqiV",
	"UdAIEYLCcxiPszwsTbsQfgN71TQXF7oHiFQXECQhAge/gUIEaeW4D1AsfngZjyNMJ0booxwW2ZUKcUXt",
	"lnkjvy4mKrtM5V4dZnxwoa1TRhozfxA77j/E+IdM3kVFBCPfV5SMcVwG8R8eltD2rBxpCge/PRn808PB",
	"T28G1/+wPVwtX00/p1Qj6jHWL/aq+LPLaIri0Ou5TM4mtucyKqKIA5qIIasPaMzgllXD7fmi28P8U6/X",
	"8e3s5HgmJBahXgbpMH2V5MVvf3LcltDTylZXeVhQvJ+0C+5QvHFUlvJilKGmZg9RW3m0UT/B5POby5om",
	"1nqBuMWRcJ40vh+/XTYP7ciZ96gIm9Kpky2Pixrfbjes2ArCwge5NpfzEgljdST5aek5gEdwjM9bLf90",
	"tG+MlwGW5C/dAwsIScjbBk8Z+oxZ86S7SHkrfYOmHaQpGmbTKWk2nHy5Y76OpRL5btkulie8db74NFm0",
	"psLEwtytb3CfIAXF83BffnZdG8+qt0V393dWBeh1NoXxgCAYCoO7un0qJcQx0nurP00thfcBB9PkFkeo",
	"D0JE71iS9gHjw7PvVuGw9KSIe64U3brINZhkZELbzqkqP4+fY1MFLNdHtOTmtxGAcGtyxMpRTTLIqDcN",
	"NJBlw9sW07dhJkRyx0G0J7GmSxaEMB/rt78FtaV2tHhcsHCIBgRZAH8lbps6LaUYCsgegMou1oArM2Wt",
	"dUDdBIjHVfUTJp2gKSIw+jmaWdRt/RXcoZm4bUhOAVdnP7c/l8pu+SWLc491QLUooxkf2UNFrNyDLs/F",
	"anQjMIqSTwCPhIMOOR6Q5ChqgFYJ/DaIWbEvA5yEA1ZF27gfppXjFMrxL1UvDAOQdglVsCQlsYO/fC+1",
	"U8b6AFZ6/FQatb5k27VExln/Pk3V79NUbeqBwYOb6vYua5JjoUsUAKjIevFhGGKaxzKIVLi5tCjFIdRJ",
	"KbQE1nSRQXnYSWlW2phtvMk4dPAJR5HO7gwIGpRsRHgEApE/MfzOw1i0FlusyPg7e+lYexn68l2ymSPY",
	"EYQjxrzI6KR1yDSjE48RN2rP3aYBd/dW1DqnqlTffszZYGl+GbfyKEji/JWdyBnWX0+Nil2weTt77kmV",
	"Abd0aJMGbay9LKOukTM9+W5vCnasWEqjUWb48/ulAocGnYpNheEU2+Fn7NR2SaYZTUVUJVeelUvTNoxP",
	"zK24WHdUWEXEqJ0i1QvyTgPWwSauHkFGMJtdceSrMvUIEkROMhmIfiv+e6Vx+rNfve+pYt1iSeJrgaIJ",
	"Y6mZp0iPgjliJwiGorHEe++fDHSzwXvlM9ErTjG/04q64TgeJbouOZSV65W06tEsTRPC/jGdJgmbPMFx",
	"UAx+JX4Db+KgVys+fjWFpMgVM4UxHCNZqWJGGZqCT5hNDMEZ4QDFFKmQw9yE3v+TuH4W9EWlA4JgJCjf",
	"MsmTP+EbVWPy3agFn6QwmCDw9Mlhr9/L+FVcQJMeD4efPn16AsXnJwkZD1VfOjx/c/ry3dXLwdMnh08m",
	"bBoZ+RX0/tVVDrwtNnly8abX790jQlVqpSeHTw71TQGmuHfce/bk8MkzmYtjImhiKJhoqHYz0O8Vx9K8",
	"Im8R6r1U748Rq1aDlrlmoHwjL2oSCoL4IUMi0aaCgHwg2TdKzOcnM9d8irRDh4e2uCz7oOo9pHXUDsMo",
	"0dn3rH9vXIj4iFVrg0gPcDsDOjdOkRXHOrdoX5q7KhGv+dVfyh6BmKeHh2ur5e8s7W0p639RpD+igCBG",
	"MLqvpnh96PeeHz5zzZpvY/gqIbc4DJEM56a6hBKnLwCjyEy1RMGBIFBhRYZcLH6UJX7AIOeA84QZXNC7",
	"5gd0Qi30e5FQGwETeeN7kYSztYHWlRX8oWzL4XrpQw3DR2tbhuk4asSp9k9YEHrYjtAXMHfqr0oDEnAg",
	"Fg7/fHldsP/Qtwm14ZcoYW/CBykm9PuOMn2cid+rFKITwtvkHBejhphTLcv4Nbm7zRNV5/bntqijAm1y",
	"K6vzIe/xvL3Hu4S9SrI4rCBNAm5ZhPW9T5vdomJjgjevzt/MoiqX7Bql72pY5+I6taxvOYmd2QR2tnsS",
	"WP/Z4DLFe50Nhzs4G6z53LdzNqxGoRLSmzlIhsoAPZB2605asyDiklWcbomkHUowjoMoC9GbuLgi15Vq",
	"1wPzVSVlx9woKiFP/RZcp2EJYZn7dKxCmPZXfqbW5TZQ7KnZsKPeuzsSvN6oxm1zNe1M6dak6kmae6mE",
	"2xbagSa7CdHhF/nH8op6maRP1Whblq7lcYNiEZu+GVipar/uCCvSU3+VQ/ZbJofDPZBf+3Sy0hQFeISD",
	"NVBcp7vJN01xG7sMLX1s7wPZf/X3o12d8UNx3dCpNVbRXTWfnegBf89vDtOTAtDXzXGabr5CntMIWCvX",
	"qfiPdhNEHijy4/LaVTI0ukZVT0bWYFAp0kyuYTAupEWWX+tYoRR3HcZ6n6xjpCmOT3SN+5r/shWBU/jZ",
	"p/cWrPCSJZp9n7LN5v2eap4mC7qMS3AaKuUIwy95EoAHb6lwkecN8Dk8U6P1XntZxDIbXSwSpPvpXimv",
	"ba2EMZQpz7x0rwqFyIQO26aT5bShTo9yxWt4vre8nioegRQShmFkT8hZ5M+ovjbnv6sXNgpabelZxEj1",
	"1xvb1coqSU0sHCNbgDwxy9enhF3IpRuY6chOEVSPTZolq2z2VWlblfBbHDFEwO1M5m4Uj5PWpzm5oqcq",
	"KQBtE5oZBZvipxyAKZIIequQRTZBpxdN50C0LGh97rL2I09QXRMDn5txjxtXa6LybO4zTC9LbKCZ9YZf",
	"1KNzbz+B6HahXqp7HVx52w1b7c8rUah7ZK0vB8guhbm+j4jcPV42wYMNSmcV5/unekbWFa6ZeTsZAE1K",
	"6WTn23eKqZaZbKWXJvPXTmxZJWJZG3GEaFnyOCt6/igJpIDc7g8SuF4iqTgTAlXTHA1SWdLch1TKrgM9",
	"gqqJvqGI8MbC81u+4jXXgrdQ1yuEQFD0ABrW1VuV+BWMKq35HTjEoxGSWVXyyvJL2bor+L+XT7RQZ7T/",
	"UnfcDLorT863il7LszULTotWqp5lBZsaQGt0VJiJZBq1Qp2T5kfnqKgkvtq8o6KUTGwd4612L99PV4fK",
	"9Lbba30tT1ND1LZmtG291Mrnc98P1PJd57oeYfgld509eIuJK8Pb1q7tmb65Pb5Cqk15PA7RCcb2+YFI",
	"dY0dCaUlCGvnhLCBQA2+Lxmgk6dT24mymBdPbCfAb+WhiN7PWqXZUKbj81BUa9R8Knt+FTRddrd5eMdU",
	"lsJKKu/99pF14Ik8CePXxxWS7KpcsS5uMMqsdeYH3fcr5AiXA1rnCs5vzigEUEfI2OpAds1q7MGKqqKp",
	"Qo2LH2ucty+cplOffn2c9lYAPpoV1f1WZjp+RWy/aH+gskDFj+uWbSZCcg0pUjB1GVDmYmpya+vER32Z",
	"mq4PEqJTWy2ZHcSx9nImr91eWwWFNd1ZRYNNX1QFNzToc3wRjZw0/CJtLg9+LPVBG2jaz6fclrPHd1G+",
	"nYaLqIDePt4+M3NhXbDfct/cFY43es00s59uWZ8WSR1dhPW13yoFEa5D9HTxlhsU2slXvufSyMMTKuC5",
	"dx5yXU57eeSrpIXdcH+lOu2/kOpsN5DwoHtxS/Elyzzx5I7JUpFFR6rM2GQoEz4PzFJdDdSYscmp6HBR",
	"FO/aSPKH0iQ7OsQ8iCAv8KXyZq/nTDtq7/Ih5thLCP4tqll5xFLy8mo6t/AExYzDAdWppPhoOHg5cYwS",
	"Mk5YB+J4JTpsmDjKk+wq5KKyCA8akUXgZBZzUa9uZWIx0sL2jj9em1Sg2hRkIGb3wrpM+92K63OVHXwT",
	"KBZj7wizfG+NkVl8bWYtv+V51oE7OYM3l6oywO0YU/WNN4QzW/XkPUOdWBtQ8LJK69VErwIBgGJYoOtL",
	"+qBQVsj1waFquSkklkv1bjnVUhsChSKjgbWu4/an7V1Ok3gU4aBN5Mp1ASgyLXXgX4risFSKw4cOqmVj",
	"N0YRrvq0+6eOmavc3kEriu3K2UrlVCIc33kTQBctq1SSdoNo37WOZS+9265iVc/mbangDRRiKGKZqETi",
	"fzZ4BVlmbGLEWG7ae9do/7/yiVVb7ZzVpuA/oNIwlBeLpJ0gyjXdJGMDGEXtTKe3dS76nERRb7ey7iSK",
	"QCJKXebBegyRKY4dVrLVIC53DUYkmQr/Sz4n+hyglOkSm93AXwkNbHn1Z+DgW4sP9EC39g6vF8erWZ1M",
	"osjTs1kc3E5KEMflbJDXNGoSb+JsV7WBali31TyrlL/nmr9Y6RSORakQcTjbvK1aNLtpZav+uhaV2Nzs",
	"Tg1QjtNP4k3pR7oOnTwDC0z4HYe3SXKna+m4SOWFbuMVA9ExzkAN3hrNnwbJVJYE3Le0y2oHPtmWNSSd",
	"57jrSL4tUKDRmWOlOamxgbzNpRRWk+zohpsjwAnw9WYN7ny5rSYNlpdZhVM7Sk3eHH5Rf3kd6nqQF7qP",
	"14F+a7TezwPdB8vriyO1BXnCZpT1WwXojxAlPhEvq8Wv3JZncrPTRNVbG+ZFzJpvKLo+21nefDMitDbP",
	"jkwDxvxu1ShvtM7URmVV50ulgN7H64eyKd9ekU5qohrJ4ADFjMyG6DNmZkSJHlrRRLlObYMO9K5S0NZD",
	"EcpigmDYTWHpLxVXuvaUgF56jwkSH+WnBMLOGlCtorBCaBkxFqwOv5j/vgkfhlNI7gYCN5z/IQsmFgHA",
	"fy6N/a40yltI7i4letsleXkBX/OV2QQC4HBEIYAcmTBcPb6b3JWwnA/cjGzfsoyNFRkr7khVrV5VbJbz",
	"DW6hEHY64tjKp5A53tI6StfXQ6HPden77pNXbkdLTK7isAkMcUYBjoECkX1C2cwulo4Ou4S404Q4xFsv",
	"xJRx9cuoCmz8lBIcIEtd2qYsbSMEWUaQa1fGZ4tAbC0zu8cPA7ZTEGH1EpnlMCxJkma9S0MgGIWpbPLA",
	"rGjoIRe+ldp5/mXLNq+eW6rfdcffEN5DHMFbHAkV0R+ZJ2a/bwmxpY3ZXBnGd4Bjueim+iM1xBVlnGEZ",
	"iI24EylBB7IKf8txbBT2X9nB5lkfzZjSqziaynCqttNZZ6WQt03Lo5QgqDKoqi/Nlrz1QqzZJcmy9I0Y",
	"qNl/wrIUYNFufRY2m8HsStThB9SccCRLR3MKhKoWrgnnRjBbaHUoa/0PAhhFtzC4a7YNlHEhl3eqe27G",
	"TCAnETDXM+2sJkWJj9r4RqFtjU9PSyTyGsZhVCGR4nUuCAqsdKMHnTVrIIb0F2U6J5TAVG/zaBDz+KWz",
	"0huXQLrP+7iyWuGRfJgzgbTCXXwIkKU6EDoZpEXZjG5g1snWZSsvC3cZ3hfl/l0Srht9VnFMPt9ueHwJ",
	"DwRNk3WrbZdiTACt59fqCB5SxAb5RcVfylYQfYXYmRpk+yhfp9VV7KG0uWYDrGhf5Uf3A8AVXsCg2jSQ",
	"AoW5nPW5XhmArvzvoxuu59H9vt6mu6isXZTVCaYsIbPOympa7l9HZBWDw0/odpIkd1p18uJl+ivVSZ7X",
	"K+hKO01u4yGo1UadfpKG+A6lyyj4AhSHaYJLkWAulJSyxwjdWZ/MAxGd44WiIoeMrBcsfz6X0T3fQmyY",
	"sSOfo1ZE02zjfpOaE4r3mzrJlso6oxp815ESFAkOTE2tIymoaiAnGUu0TPr608cVm9nd45piBQ1xqdVj",
	"dt11ZZ6um7VeEpIQH94aQRzVo93aXMC6Nk1N/5DZ0oqbZi1wssIuPoHg3RLtbiaZ7bes4+Tpq3x0HM/c",
	"rW4lpzyAQRk5msuUIRNKIB8CySvb7BXM5KrqaWi7gg7ax/GAoGe62m81Er1DerbtuWMqM7qxKDNstBfz",
	"Egk18mJeW7gZiQoJHvTvWcjJRfblykzmSSJ/aLbZV6GyqQBcMcWOwm8VJloLsoTh7gNwT8JQeQtclTZy",
	"tFaJv0s5LQPte1JOazc1VjZqneyCQZKMcITa5Zdqt6s0XmqdG5T/mTGNATOZ47Ipp1sNPusXZXubYk1t",
	"e31Z1pwp0NyYqd2QXiBIECndjxC5twe0XZAkzGSUrGzU6/cyEvWOexPGUno8HMIUP6HTJGGTJzgOhvdH",
	"PUt0GINjqTtYh6Dy88BnqDN0j6IkVd6o2nDHw2GUBDCaJJQdf3/4/SFfnxjpOofMF2tOs9IbJwDjEOg3",
	"VfpBv5K1lddQ9SWK8aZ5gieQcwQtBpHIsQTxlaRgMUjRUwmpelczOseM2+BbycP/9CBmBIZ7KK3r2daR",
	"a3v17voKLhzuY6IyAChbpAGEPKTatoKSuUL46uPQMDHnG1G3cvcmCOJ0UkVAHuLvwF81alh1K8eR1vvK",
	"JF/5XoVVwQxkspb7scK3ufBP95lPLz+cGaQowOkz73lSKtTrN7EQSSLRHqaMFHTYNGE1L5rfTFFFVTSn",
	"rM/hrILWDZ6aLyw7TLggxeOJZYOWnNK+01aZoa+KK9O+mHSEYxgHGEZWWdNQdvnh+uHvAwAA//9bfOJ/",
	"fB8BAA==",
}

// GetSwagger returns the content of the embedded swagger specification file
// or error if failed to decode
func decodeSpec() ([]byte, error) {
	zipped, err := base64.StdEncoding.DecodeString(strings.Join(swaggerSpec, ""))
	if err != nil {
		return nil, fmt.Errorf("error base64 decoding spec: %w", err)
	}
	zr, err := gzip.NewReader(bytes.NewReader(zipped))
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}
	var buf bytes.Buffer
	_, err = buf.ReadFrom(zr)
	if err != nil {
		return nil, fmt.Errorf("error decompressing spec: %w", err)
	}

	return buf.Bytes(), nil
}

var rawSpec = decodeSpecCached()

// a naive cached of a decoded swagger spec
func decodeSpecCached() func() ([]byte, error) {
	data, err := decodeSpec()
	return func() ([]byte, error) {
		return data, err
	}
}

// Constructs a synthetic filesystem for resolving external references when loading openapi specifications.
func PathToRawSpec(pathToFile string) map[string]func() ([]byte, error) {
	res := make(map[string]func() ([]byte, error))
	if len(pathToFile) > 0 {
		res[pathToFile] = rawSpec
	}

	return res
}

// GetSwagger returns the Swagger specification corresponding to the generated code
// in this file. The external references of Swagger specification are resolved.
// The logic of resolving external references is tightly connected to "import-mapping" feature.
// Externally referenced files must be embedded in the corresponding golang packages.
// Urls can be supported but this task was out of the scope.
func GetSwagger() (swagger *openapi3.T, err error) {
	resolvePath := PathToRawSpec("")

	loader := openapi3.NewLoader()
	loader.IsExternalRefsAllowed = true
	loader.ReadFromURIFunc = func(loader *openapi3.Loader, url *url.URL) ([]byte, error) {
		pathToFile := url.String()
		pathToFile = path.Clean(pathToFile)
		getSpec, ok := resolvePath[pathToFile]
		if !ok {
			err1 := fmt.Errorf("path not found: %s", pathToFile)
			return nil, err1
		}
		return getSpec()
	}
	var specData []byte
	specData, err = rawSpec()
	if err != nil {
		return
	}
	swagger, err = loader.LoadFromData(specData)
	if err != nil {
		return
	}
	return
}
