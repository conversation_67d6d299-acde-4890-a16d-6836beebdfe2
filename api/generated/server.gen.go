// Package api provides primitives to interact with the openapi HTTP API.
//
// Code generated by github.com/oapi-codegen/oapi-codegen/v2 version v2.4.1 DO NOT EDIT.
package api

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/oapi-codegen/runtime"
	openapi_types "github.com/oapi-codegen/runtime/types"
)

// ServerInterface represents all server handlers.
type ServerInterface interface {
	// Get all parking lots (admin)
	// (GET /admin/parking-lots)
	GetAdminParkingLots(c *gin.Context, params GetAdminParkingLotsParams)
	// Create new parking lot
	// (POST /admin/parking-lots)
	PostAdminParkingLots(c *gin.Context)
	// Delete parking lot
	// (DELETE /admin/parking-lots/{lotId})
	DeleteAdminParkingLotsLotId(c *gin.Context, lotId openapi_types.UUID)
	// Get parking lot details (admin)
	// (GET /admin/parking-lots/{lotId})
	GetAdminParkingLotsLotId(c *gin.Context, lotId openapi_types.UUID)
	// Update parking lot
	// (PUT /admin/parking-lots/{lotId})
	PutAdminParkingLotsLotId(c *gin.Context, lotId openapi_types.UUID)
	// Get parking lot pricing configurations
	// (GET /admin/parking-lots/{lotId}/pricing-configs)
	GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId openapi_types.UUID, params GetAdminParkingLotsLotIdPricingConfigsParams)
	// Create new pricing configuration
	// (POST /admin/parking-lots/{lotId}/pricing-configs)
	PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context, lotId openapi_types.UUID)
	// Delete pricing configuration
	// (DELETE /admin/parking-lots/{lotId}/pricing-configs/{configId})
	DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId openapi_types.UUID, configId openapi_types.UUID)
	// Get specific pricing configuration
	// (GET /admin/parking-lots/{lotId}/pricing-configs/{configId})
	GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId openapi_types.UUID, configId openapi_types.UUID)
	// Update pricing configuration
	// (PUT /admin/parking-lots/{lotId}/pricing-configs/{configId})
	PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context, lotId openapi_types.UUID, configId openapi_types.UUID)
	// Activate pricing configuration
	// (POST /admin/parking-lots/{lotId}/pricing-configs/{configId}/activate)
	PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context, lotId openapi_types.UUID, configId openapi_types.UUID)
	// Get all payments (admin)
	// (GET /admin/payments)
	GetAdminPayments(c *gin.Context, params GetAdminPaymentsParams)
	// Get payment details (admin)
	// (GET /admin/payments/{paymentId})
	GetAdminPaymentsPaymentId(c *gin.Context, paymentId openapi_types.UUID)
	// Process refund
	// (POST /admin/payments/{paymentId}/refund)
	PostAdminPaymentsPaymentIdRefund(c *gin.Context, paymentId openapi_types.UUID)
	// Get all license plates (admin)
	// (GET /admin/plates)
	GetAdminPlates(c *gin.Context, params GetAdminPlatesParams)
	// Delete license plate (admin)
	// (DELETE /admin/plates/{plateId})
	DeleteAdminPlatesPlateId(c *gin.Context, plateId openapi_types.UUID)
	// Get license plate details (admin)
	// (GET /admin/plates/{plateId})
	GetAdminPlatesPlateId(c *gin.Context, plateId openapi_types.UUID)
	// Activate license plate
	// (POST /admin/plates/{plateId}/activate)
	PostAdminPlatesPlateIdActivate(c *gin.Context, plateId openapi_types.UUID)
	// Deactivate license plate
	// (POST /admin/plates/{plateId}/deactivate)
	PostAdminPlatesPlateIdDeactivate(c *gin.Context, plateId openapi_types.UUID)
	// Preview fee calculation for different scenarios
	// (POST /admin/pricing-configs/calculate-preview)
	PostAdminPricingConfigsCalculatePreview(c *gin.Context)
	// Validate pricing configuration
	// (POST /admin/pricing-configs/validate)
	PostAdminPricingConfigsValidate(c *gin.Context)
	// Get all parking sessions (admin)
	// (GET /admin/sessions)
	GetAdminSessions(c *gin.Context, params GetAdminSessionsParams)
	// Get parking session details (admin)
	// (GET /admin/sessions/{sessionId})
	GetAdminSessionsSessionId(c *gin.Context, sessionId openapi_types.UUID)
	// Update parking session (admin)
	// (PUT /admin/sessions/{sessionId})
	PutAdminSessionsSessionId(c *gin.Context, sessionId openapi_types.UUID)
	// Cancel parking session
	// (POST /admin/sessions/{sessionId}/cancel)
	PostAdminSessionsSessionIdCancel(c *gin.Context, sessionId openapi_types.UUID)
	// Manually complete parking session
	// (POST /admin/sessions/{sessionId}/complete)
	PostAdminSessionsSessionIdComplete(c *gin.Context, sessionId openapi_types.UUID)
	// Get all users (admin)
	// (GET /admin/users)
	GetAdminUsers(c *gin.Context, params GetAdminUsersParams)
	// Get user details (admin)
	// (GET /admin/users/{userId})
	GetAdminUsersUserId(c *gin.Context, userId openapi_types.UUID)
	// Update user (admin)
	// (PUT /admin/users/{userId})
	PutAdminUsersUserId(c *gin.Context, userId openapi_types.UUID)
	// Activate user
	// (POST /admin/users/{userId}/activate)
	PostAdminUsersUserIdActivate(c *gin.Context, userId openapi_types.UUID)
	// Suspend user
	// (POST /admin/users/{userId}/suspend)
	PostAdminUsersUserIdSuspend(c *gin.Context, userId openapi_types.UUID)
	// Change password for authenticated user
	// (POST /auth/change-password)
	PostAuthChangePassword(c *gin.Context)
	// Request password reset
	// (POST /auth/forgot-password)
	PostAuthForgotPassword(c *gin.Context)
	// Login user
	// (POST /auth/login)
	PostAuthLogin(c *gin.Context)
	// Refresh access token
	// (POST /auth/refresh)
	PostAuthRefresh(c *gin.Context)
	// Register a new user
	// (POST /auth/register)
	PostAuthRegister(c *gin.Context)
	// Resend email verification link
	// (POST /auth/resend-verification)
	PostAuthResendVerification(c *gin.Context)
	// Reset password using token
	// (POST /auth/reset-password)
	PostAuthResetPassword(c *gin.Context)
	// Get user's active sessions
	// (GET /auth/sessions)
	GetAuthSessions(c *gin.Context)
	// Logout from all sessions except current
	// (POST /auth/sessions/logout-all)
	PostAuthSessionsLogoutAll(c *gin.Context)
	// Logout from specific session
	// (DELETE /auth/sessions/{sessionId})
	DeleteAuthSessionsSessionId(c *gin.Context, sessionId openapi_types.UUID)
	// Verify email address using magic link token
	// (GET /auth/verify-email)
	GetAuthVerifyEmail(c *gin.Context, params GetAuthVerifyEmailParams)
	// Get user's bookings
	// (GET /bookings)
	GetBookings(c *gin.Context, params GetBookingsParams)
	// Create a new booking
	// (POST /bookings)
	PostBookings(c *gin.Context)
	// Cancel a booking
	// (DELETE /bookings/{bookingId})
	DeleteBookingsBookingId(c *gin.Context, bookingId openapi_types.UUID)
	// Get booking details
	// (GET /bookings/{bookingId})
	GetBookingsBookingId(c *gin.Context, bookingId openapi_types.UUID)
	// License plate detection from hardware (entry/exit)
	// (POST /hardware/detection)
	PostHardwareDetection(c *gin.Context)
	// Get user's notifications
	// (GET /notifications)
	GetNotifications(c *gin.Context, params GetNotificationsParams)
	// Mark notification as read
	// (PATCH /notifications/{notificationId}/mark-read)
	PatchNotificationsNotificationIdMarkRead(c *gin.Context, notificationId openapi_types.UUID)
	// Search parking lots
	// (GET /parking-lots)
	GetParkingLots(c *gin.Context, params GetParkingLotsParams)
	// Get parking lot details
	// (GET /parking-lots/{lotId})
	GetParkingLotsLotId(c *gin.Context, lotId openapi_types.UUID)
	// Get real-time availability
	// (GET /parking-lots/{lotId}/availability)
	GetParkingLotsLotIdAvailability(c *gin.Context, lotId openapi_types.UUID)
	// Get user's saved payment methods
	// (GET /payment-methods)
	GetPaymentMethods(c *gin.Context)
	// Create Stripe setup intent for adding a new payment method
	// (POST /payment-methods)
	PostPaymentMethods(c *gin.Context)
	// Handle Stripe setup completion callback
	// (POST /payment-methods/stripe-callback)
	PostPaymentMethodsStripeCallback(c *gin.Context)
	// Validate if user has payment method set up for auto-payments
	// (GET /payment-methods/validate-setup)
	GetPaymentMethodsValidateSetup(c *gin.Context)
	// Remove a saved payment method
	// (DELETE /payment-methods/{paymentMethodId})
	DeletePaymentMethodsPaymentMethodId(c *gin.Context, paymentMethodId string)
	// Set payment method as default for automatic payments
	// (POST /payment-methods/{paymentMethodId}/set-default)
	PostPaymentMethodsPaymentMethodIdSetDefault(c *gin.Context, paymentMethodId string)
	// Get user's payment history
	// (GET /payments)
	GetPayments(c *gin.Context, params GetPaymentsParams)
	// Stripe webhook endpoint
	// (POST /payments/webhooks/stripe)
	PostPaymentsWebhooksStripe(c *gin.Context)
	// Create Stripe payment link for session (manual payment)
	// (POST /payments/{sessionId}/create-payment-link)
	PostPaymentsSessionIdCreatePaymentLink(c *gin.Context, sessionId openapi_types.UUID)
	// Process automatic payment for completed session
	// (POST /payments/{sessionId}/process-auto-payment)
	PostPaymentsSessionIdProcessAutoPayment(c *gin.Context, sessionId openapi_types.UUID)
	// Get user's parking sessions
	// (GET /sessions)
	GetSessions(c *gin.Context, params GetSessionsParams)
	// Get user's active parking sessions
	// (GET /sessions/active)
	GetSessionsActive(c *gin.Context)
	// Get parking session details
	// (GET /sessions/{sessionId})
	GetSessionsSessionId(c *gin.Context, sessionId openapi_types.UUID)
	// Get user's license plates
	// (GET /users/plates)
	GetUsersPlates(c *gin.Context)
	// Add a new license plate
	// (POST /users/plates)
	PostUsersPlates(c *gin.Context)
	// Remove a license plate
	// (DELETE /users/plates/{plateId})
	DeleteUsersPlatesPlateId(c *gin.Context, plateId openapi_types.UUID)
	// Get user profile
	// (GET /users/profile)
	GetUsersProfile(c *gin.Context)
	// Update user profile
	// (PUT /users/profile)
	PutUsersProfile(c *gin.Context)
}

// ServerInterfaceWrapper converts contexts to parameters.
type ServerInterfaceWrapper struct {
	Handler            ServerInterface
	HandlerMiddlewares []MiddlewareFunc
	ErrorHandler       func(*gin.Context, error, int)
}

type MiddlewareFunc func(c *gin.Context)

// GetAdminParkingLots operation middleware
func (siw *ServerInterfaceWrapper) GetAdminParkingLots(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAdminParkingLotsParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminParkingLots(c, params)
}

// PostAdminParkingLots operation middleware
func (siw *ServerInterfaceWrapper) PostAdminParkingLots(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminParkingLots(c)
}

// DeleteAdminParkingLotsLotId operation middleware
func (siw *ServerInterfaceWrapper) DeleteAdminParkingLotsLotId(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteAdminParkingLotsLotId(c, lotId)
}

// GetAdminParkingLotsLotId operation middleware
func (siw *ServerInterfaceWrapper) GetAdminParkingLotsLotId(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminParkingLotsLotId(c, lotId)
}

// PutAdminParkingLotsLotId operation middleware
func (siw *ServerInterfaceWrapper) PutAdminParkingLotsLotId(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutAdminParkingLotsLotId(c, lotId)
}

// GetAdminParkingLotsLotIdPricingConfigs operation middleware
func (siw *ServerInterfaceWrapper) GetAdminParkingLotsLotIdPricingConfigs(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAdminParkingLotsLotIdPricingConfigsParams

	// ------------- Optional query parameter "includeInactive" -------------

	err = runtime.BindQueryParameter("form", true, false, "includeInactive", c.Request.URL.Query(), &params.IncludeInactive)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter includeInactive: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminParkingLotsLotIdPricingConfigs(c, lotId, params)
}

// PostAdminParkingLotsLotIdPricingConfigs operation middleware
func (siw *ServerInterfaceWrapper) PostAdminParkingLotsLotIdPricingConfigs(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminParkingLotsLotIdPricingConfigs(c, lotId)
}

// DeleteAdminParkingLotsLotIdPricingConfigsConfigId operation middleware
func (siw *ServerInterfaceWrapper) DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "configId" -------------
	var configId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "configId", c.Param("configId"), &configId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter configId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}

// GetAdminParkingLotsLotIdPricingConfigsConfigId operation middleware
func (siw *ServerInterfaceWrapper) GetAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "configId" -------------
	var configId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "configId", c.Param("configId"), &configId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter configId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}

// PutAdminParkingLotsLotIdPricingConfigsConfigId operation middleware
func (siw *ServerInterfaceWrapper) PutAdminParkingLotsLotIdPricingConfigsConfigId(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "configId" -------------
	var configId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "configId", c.Param("configId"), &configId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter configId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutAdminParkingLotsLotIdPricingConfigsConfigId(c, lotId, configId)
}

// PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate operation middleware
func (siw *ServerInterfaceWrapper) PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Path parameter "configId" -------------
	var configId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "configId", c.Param("configId"), &configId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter configId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate(c, lotId, configId)
}

// GetAdminPayments operation middleware
func (siw *ServerInterfaceWrapper) GetAdminPayments(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAdminPaymentsParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "userId" -------------

	err = runtime.BindQueryParameter("form", true, false, "userId", c.Request.URL.Query(), &params.UserId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sessionId" -------------

	err = runtime.BindQueryParameter("form", true, false, "sessionId", c.Request.URL.Query(), &params.SessionId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "dateFrom" -------------

	err = runtime.BindQueryParameter("form", true, false, "dateFrom", c.Request.URL.Query(), &params.DateFrom)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter dateFrom: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "dateTo" -------------

	err = runtime.BindQueryParameter("form", true, false, "dateTo", c.Request.URL.Query(), &params.DateTo)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter dateTo: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "minAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "minAmount", c.Request.URL.Query(), &params.MinAmount)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter minAmount: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "maxAmount" -------------

	err = runtime.BindQueryParameter("form", true, false, "maxAmount", c.Request.URL.Query(), &params.MaxAmount)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter maxAmount: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminPayments(c, params)
}

// GetAdminPaymentsPaymentId operation middleware
func (siw *ServerInterfaceWrapper) GetAdminPaymentsPaymentId(c *gin.Context) {

	var err error

	// ------------- Path parameter "paymentId" -------------
	var paymentId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "paymentId", c.Param("paymentId"), &paymentId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter paymentId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminPaymentsPaymentId(c, paymentId)
}

// PostAdminPaymentsPaymentIdRefund operation middleware
func (siw *ServerInterfaceWrapper) PostAdminPaymentsPaymentIdRefund(c *gin.Context) {

	var err error

	// ------------- Path parameter "paymentId" -------------
	var paymentId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "paymentId", c.Param("paymentId"), &paymentId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter paymentId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminPaymentsPaymentIdRefund(c, paymentId)
}

// GetAdminPlates operation middleware
func (siw *ServerInterfaceWrapper) GetAdminPlates(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAdminPlatesParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "userId" -------------

	err = runtime.BindQueryParameter("form", true, false, "userId", c.Request.URL.Query(), &params.UserId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "plateNumber" -------------

	err = runtime.BindQueryParameter("form", true, false, "plateNumber", c.Request.URL.Query(), &params.PlateNumber)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateNumber: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "plateType" -------------

	err = runtime.BindQueryParameter("form", true, false, "plateType", c.Request.URL.Query(), &params.PlateType)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateType: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "isActive" -------------

	err = runtime.BindQueryParameter("form", true, false, "isActive", c.Request.URL.Query(), &params.IsActive)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter isActive: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminPlates(c, params)
}

// DeleteAdminPlatesPlateId operation middleware
func (siw *ServerInterfaceWrapper) DeleteAdminPlatesPlateId(c *gin.Context) {

	var err error

	// ------------- Path parameter "plateId" -------------
	var plateId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "plateId", c.Param("plateId"), &plateId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteAdminPlatesPlateId(c, plateId)
}

// GetAdminPlatesPlateId operation middleware
func (siw *ServerInterfaceWrapper) GetAdminPlatesPlateId(c *gin.Context) {

	var err error

	// ------------- Path parameter "plateId" -------------
	var plateId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "plateId", c.Param("plateId"), &plateId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminPlatesPlateId(c, plateId)
}

// PostAdminPlatesPlateIdActivate operation middleware
func (siw *ServerInterfaceWrapper) PostAdminPlatesPlateIdActivate(c *gin.Context) {

	var err error

	// ------------- Path parameter "plateId" -------------
	var plateId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "plateId", c.Param("plateId"), &plateId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminPlatesPlateIdActivate(c, plateId)
}

// PostAdminPlatesPlateIdDeactivate operation middleware
func (siw *ServerInterfaceWrapper) PostAdminPlatesPlateIdDeactivate(c *gin.Context) {

	var err error

	// ------------- Path parameter "plateId" -------------
	var plateId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "plateId", c.Param("plateId"), &plateId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminPlatesPlateIdDeactivate(c, plateId)
}

// PostAdminPricingConfigsCalculatePreview operation middleware
func (siw *ServerInterfaceWrapper) PostAdminPricingConfigsCalculatePreview(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminPricingConfigsCalculatePreview(c)
}

// PostAdminPricingConfigsValidate operation middleware
func (siw *ServerInterfaceWrapper) PostAdminPricingConfigsValidate(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminPricingConfigsValidate(c)
}

// GetAdminSessions operation middleware
func (siw *ServerInterfaceWrapper) GetAdminSessions(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAdminSessionsParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "userId" -------------

	err = runtime.BindQueryParameter("form", true, false, "userId", c.Request.URL.Query(), &params.UserId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "parkingLotId" -------------

	err = runtime.BindQueryParameter("form", true, false, "parkingLotId", c.Request.URL.Query(), &params.ParkingLotId)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter parkingLotId: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "plateNumber" -------------

	err = runtime.BindQueryParameter("form", true, false, "plateNumber", c.Request.URL.Query(), &params.PlateNumber)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateNumber: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "dateFrom" -------------

	err = runtime.BindQueryParameter("form", true, false, "dateFrom", c.Request.URL.Query(), &params.DateFrom)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter dateFrom: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "dateTo" -------------

	err = runtime.BindQueryParameter("form", true, false, "dateTo", c.Request.URL.Query(), &params.DateTo)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter dateTo: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "isPaid" -------------

	err = runtime.BindQueryParameter("form", true, false, "isPaid", c.Request.URL.Query(), &params.IsPaid)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter isPaid: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminSessions(c, params)
}

// GetAdminSessionsSessionId operation middleware
func (siw *ServerInterfaceWrapper) GetAdminSessionsSessionId(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminSessionsSessionId(c, sessionId)
}

// PutAdminSessionsSessionId operation middleware
func (siw *ServerInterfaceWrapper) PutAdminSessionsSessionId(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutAdminSessionsSessionId(c, sessionId)
}

// PostAdminSessionsSessionIdCancel operation middleware
func (siw *ServerInterfaceWrapper) PostAdminSessionsSessionIdCancel(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminSessionsSessionIdCancel(c, sessionId)
}

// PostAdminSessionsSessionIdComplete operation middleware
func (siw *ServerInterfaceWrapper) PostAdminSessionsSessionIdComplete(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminSessionsSessionIdComplete(c, sessionId)
}

// GetAdminUsers operation middleware
func (siw *ServerInterfaceWrapper) GetAdminUsers(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAdminUsersParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "role" -------------

	err = runtime.BindQueryParameter("form", true, false, "role", c.Request.URL.Query(), &params.Role)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter role: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "search" -------------

	err = runtime.BindQueryParameter("form", true, false, "search", c.Request.URL.Query(), &params.Search)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter search: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "emailVerified" -------------

	err = runtime.BindQueryParameter("form", true, false, "emailVerified", c.Request.URL.Query(), &params.EmailVerified)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter emailVerified: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminUsers(c, params)
}

// GetAdminUsersUserId operation middleware
func (siw *ServerInterfaceWrapper) GetAdminUsersUserId(c *gin.Context) {

	var err error

	// ------------- Path parameter "userId" -------------
	var userId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "userId", c.Param("userId"), &userId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAdminUsersUserId(c, userId)
}

// PutAdminUsersUserId operation middleware
func (siw *ServerInterfaceWrapper) PutAdminUsersUserId(c *gin.Context) {

	var err error

	// ------------- Path parameter "userId" -------------
	var userId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "userId", c.Param("userId"), &userId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutAdminUsersUserId(c, userId)
}

// PostAdminUsersUserIdActivate operation middleware
func (siw *ServerInterfaceWrapper) PostAdminUsersUserIdActivate(c *gin.Context) {

	var err error

	// ------------- Path parameter "userId" -------------
	var userId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "userId", c.Param("userId"), &userId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminUsersUserIdActivate(c, userId)
}

// PostAdminUsersUserIdSuspend operation middleware
func (siw *ServerInterfaceWrapper) PostAdminUsersUserIdSuspend(c *gin.Context) {

	var err error

	// ------------- Path parameter "userId" -------------
	var userId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "userId", c.Param("userId"), &userId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter userId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAdminUsersUserIdSuspend(c, userId)
}

// PostAuthChangePassword operation middleware
func (siw *ServerInterfaceWrapper) PostAuthChangePassword(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthChangePassword(c)
}

// PostAuthForgotPassword operation middleware
func (siw *ServerInterfaceWrapper) PostAuthForgotPassword(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthForgotPassword(c)
}

// PostAuthLogin operation middleware
func (siw *ServerInterfaceWrapper) PostAuthLogin(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthLogin(c)
}

// PostAuthRefresh operation middleware
func (siw *ServerInterfaceWrapper) PostAuthRefresh(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthRefresh(c)
}

// PostAuthRegister operation middleware
func (siw *ServerInterfaceWrapper) PostAuthRegister(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthRegister(c)
}

// PostAuthResendVerification operation middleware
func (siw *ServerInterfaceWrapper) PostAuthResendVerification(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthResendVerification(c)
}

// PostAuthResetPassword operation middleware
func (siw *ServerInterfaceWrapper) PostAuthResetPassword(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthResetPassword(c)
}

// GetAuthSessions operation middleware
func (siw *ServerInterfaceWrapper) GetAuthSessions(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthSessions(c)
}

// PostAuthSessionsLogoutAll operation middleware
func (siw *ServerInterfaceWrapper) PostAuthSessionsLogoutAll(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostAuthSessionsLogoutAll(c)
}

// DeleteAuthSessionsSessionId operation middleware
func (siw *ServerInterfaceWrapper) DeleteAuthSessionsSessionId(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteAuthSessionsSessionId(c, sessionId)
}

// GetAuthVerifyEmail operation middleware
func (siw *ServerInterfaceWrapper) GetAuthVerifyEmail(c *gin.Context) {

	var err error

	// Parameter object where we will unmarshal all parameters from the context
	var params GetAuthVerifyEmailParams

	// ------------- Required query parameter "token" -------------

	if paramValue := c.Query("token"); paramValue != "" {

	} else {
		siw.ErrorHandler(c, fmt.Errorf("Query argument token is required, but not found"), http.StatusBadRequest)
		return
	}

	err = runtime.BindQueryParameter("form", true, true, "token", c.Request.URL.Query(), &params.Token)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter token: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetAuthVerifyEmail(c, params)
}

// GetBookings operation middleware
func (siw *ServerInterfaceWrapper) GetBookings(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetBookingsParams

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "upcoming" -------------

	err = runtime.BindQueryParameter("form", true, false, "upcoming", c.Request.URL.Query(), &params.Upcoming)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter upcoming: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetBookings(c, params)
}

// PostBookings operation middleware
func (siw *ServerInterfaceWrapper) PostBookings(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostBookings(c)
}

// DeleteBookingsBookingId operation middleware
func (siw *ServerInterfaceWrapper) DeleteBookingsBookingId(c *gin.Context) {

	var err error

	// ------------- Path parameter "bookingId" -------------
	var bookingId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "bookingId", c.Param("bookingId"), &bookingId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter bookingId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteBookingsBookingId(c, bookingId)
}

// GetBookingsBookingId operation middleware
func (siw *ServerInterfaceWrapper) GetBookingsBookingId(c *gin.Context) {

	var err error

	// ------------- Path parameter "bookingId" -------------
	var bookingId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "bookingId", c.Param("bookingId"), &bookingId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter bookingId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetBookingsBookingId(c, bookingId)
}

// PostHardwareDetection operation middleware
func (siw *ServerInterfaceWrapper) PostHardwareDetection(c *gin.Context) {

	c.Set(HardwareAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostHardwareDetection(c)
}

// GetNotifications operation middleware
func (siw *ServerInterfaceWrapper) GetNotifications(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetNotificationsParams

	// ------------- Optional query parameter "unread" -------------

	err = runtime.BindQueryParameter("form", true, false, "unread", c.Request.URL.Query(), &params.Unread)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter unread: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetNotifications(c, params)
}

// PatchNotificationsNotificationIdMarkRead operation middleware
func (siw *ServerInterfaceWrapper) PatchNotificationsNotificationIdMarkRead(c *gin.Context) {

	var err error

	// ------------- Path parameter "notificationId" -------------
	var notificationId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "notificationId", c.Param("notificationId"), &notificationId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter notificationId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PatchNotificationsNotificationIdMarkRead(c, notificationId)
}

// GetParkingLots operation middleware
func (siw *ServerInterfaceWrapper) GetParkingLots(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetParkingLotsParams

	// ------------- Optional query parameter "lat" -------------

	err = runtime.BindQueryParameter("form", true, false, "lat", c.Request.URL.Query(), &params.Lat)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lat: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "lng" -------------

	err = runtime.BindQueryParameter("form", true, false, "lng", c.Request.URL.Query(), &params.Lng)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lng: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "radius" -------------

	err = runtime.BindQueryParameter("form", true, false, "radius", c.Request.URL.Query(), &params.Radius)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter radius: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "sort" -------------

	err = runtime.BindQueryParameter("form", true, false, "sort", c.Request.URL.Query(), &params.Sort)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sort: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "features" -------------

	err = runtime.BindQueryParameter("form", true, false, "features", c.Request.URL.Query(), &params.Features)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter features: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetParkingLots(c, params)
}

// GetParkingLotsLotId operation middleware
func (siw *ServerInterfaceWrapper) GetParkingLotsLotId(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetParkingLotsLotId(c, lotId)
}

// GetParkingLotsLotIdAvailability operation middleware
func (siw *ServerInterfaceWrapper) GetParkingLotsLotIdAvailability(c *gin.Context) {

	var err error

	// ------------- Path parameter "lotId" -------------
	var lotId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "lotId", c.Param("lotId"), &lotId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter lotId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetParkingLotsLotIdAvailability(c, lotId)
}

// GetPaymentMethods operation middleware
func (siw *ServerInterfaceWrapper) GetPaymentMethods(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetPaymentMethods(c)
}

// PostPaymentMethods operation middleware
func (siw *ServerInterfaceWrapper) PostPaymentMethods(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostPaymentMethods(c)
}

// PostPaymentMethodsStripeCallback operation middleware
func (siw *ServerInterfaceWrapper) PostPaymentMethodsStripeCallback(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostPaymentMethodsStripeCallback(c)
}

// GetPaymentMethodsValidateSetup operation middleware
func (siw *ServerInterfaceWrapper) GetPaymentMethodsValidateSetup(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetPaymentMethodsValidateSetup(c)
}

// DeletePaymentMethodsPaymentMethodId operation middleware
func (siw *ServerInterfaceWrapper) DeletePaymentMethodsPaymentMethodId(c *gin.Context) {

	var err error

	// ------------- Path parameter "paymentMethodId" -------------
	var paymentMethodId string

	err = runtime.BindStyledParameterWithOptions("simple", "paymentMethodId", c.Param("paymentMethodId"), &paymentMethodId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter paymentMethodId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeletePaymentMethodsPaymentMethodId(c, paymentMethodId)
}

// PostPaymentMethodsPaymentMethodIdSetDefault operation middleware
func (siw *ServerInterfaceWrapper) PostPaymentMethodsPaymentMethodIdSetDefault(c *gin.Context) {

	var err error

	// ------------- Path parameter "paymentMethodId" -------------
	var paymentMethodId string

	err = runtime.BindStyledParameterWithOptions("simple", "paymentMethodId", c.Param("paymentMethodId"), &paymentMethodId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter paymentMethodId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostPaymentMethodsPaymentMethodIdSetDefault(c, paymentMethodId)
}

// GetPayments operation middleware
func (siw *ServerInterfaceWrapper) GetPayments(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetPaymentsParams

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetPayments(c, params)
}

// PostPaymentsWebhooksStripe operation middleware
func (siw *ServerInterfaceWrapper) PostPaymentsWebhooksStripe(c *gin.Context) {

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostPaymentsWebhooksStripe(c)
}

// PostPaymentsSessionIdCreatePaymentLink operation middleware
func (siw *ServerInterfaceWrapper) PostPaymentsSessionIdCreatePaymentLink(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostPaymentsSessionIdCreatePaymentLink(c, sessionId)
}

// PostPaymentsSessionIdProcessAutoPayment operation middleware
func (siw *ServerInterfaceWrapper) PostPaymentsSessionIdProcessAutoPayment(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(HardwareAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostPaymentsSessionIdProcessAutoPayment(c, sessionId)
}

// GetSessions operation middleware
func (siw *ServerInterfaceWrapper) GetSessions(c *gin.Context) {

	var err error

	c.Set(BearerAuthScopes, []string{})

	// Parameter object where we will unmarshal all parameters from the context
	var params GetSessionsParams

	// ------------- Optional query parameter "status" -------------

	err = runtime.BindQueryParameter("form", true, false, "status", c.Request.URL.Query(), &params.Status)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter status: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "limit" -------------

	err = runtime.BindQueryParameter("form", true, false, "limit", c.Request.URL.Query(), &params.Limit)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter limit: %w", err), http.StatusBadRequest)
		return
	}

	// ------------- Optional query parameter "offset" -------------

	err = runtime.BindQueryParameter("form", true, false, "offset", c.Request.URL.Query(), &params.Offset)
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter offset: %w", err), http.StatusBadRequest)
		return
	}

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSessions(c, params)
}

// GetSessionsActive operation middleware
func (siw *ServerInterfaceWrapper) GetSessionsActive(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSessionsActive(c)
}

// GetSessionsSessionId operation middleware
func (siw *ServerInterfaceWrapper) GetSessionsSessionId(c *gin.Context) {

	var err error

	// ------------- Path parameter "sessionId" -------------
	var sessionId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "sessionId", c.Param("sessionId"), &sessionId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter sessionId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetSessionsSessionId(c, sessionId)
}

// GetUsersPlates operation middleware
func (siw *ServerInterfaceWrapper) GetUsersPlates(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsersPlates(c)
}

// PostUsersPlates operation middleware
func (siw *ServerInterfaceWrapper) PostUsersPlates(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PostUsersPlates(c)
}

// DeleteUsersPlatesPlateId operation middleware
func (siw *ServerInterfaceWrapper) DeleteUsersPlatesPlateId(c *gin.Context) {

	var err error

	// ------------- Path parameter "plateId" -------------
	var plateId openapi_types.UUID

	err = runtime.BindStyledParameterWithOptions("simple", "plateId", c.Param("plateId"), &plateId, runtime.BindStyledParameterOptions{Explode: false, Required: true})
	if err != nil {
		siw.ErrorHandler(c, fmt.Errorf("Invalid format for parameter plateId: %w", err), http.StatusBadRequest)
		return
	}

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.DeleteUsersPlatesPlateId(c, plateId)
}

// GetUsersProfile operation middleware
func (siw *ServerInterfaceWrapper) GetUsersProfile(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.GetUsersProfile(c)
}

// PutUsersProfile operation middleware
func (siw *ServerInterfaceWrapper) PutUsersProfile(c *gin.Context) {

	c.Set(BearerAuthScopes, []string{})

	for _, middleware := range siw.HandlerMiddlewares {
		middleware(c)
		if c.IsAborted() {
			return
		}
	}

	siw.Handler.PutUsersProfile(c)
}

// GinServerOptions provides options for the Gin server.
type GinServerOptions struct {
	BaseURL      string
	Middlewares  []MiddlewareFunc
	ErrorHandler func(*gin.Context, error, int)
}

// RegisterHandlers creates http.Handler with routing matching OpenAPI spec.
func RegisterHandlers(router gin.IRouter, si ServerInterface) {
	RegisterHandlersWithOptions(router, si, GinServerOptions{})
}

// RegisterHandlersWithOptions creates http.Handler with additional options
func RegisterHandlersWithOptions(router gin.IRouter, si ServerInterface, options GinServerOptions) {
	errorHandler := options.ErrorHandler
	if errorHandler == nil {
		errorHandler = func(c *gin.Context, err error, statusCode int) {
			c.JSON(statusCode, gin.H{"msg": err.Error()})
		}
	}

	wrapper := ServerInterfaceWrapper{
		Handler:            si,
		HandlerMiddlewares: options.Middlewares,
		ErrorHandler:       errorHandler,
	}

	router.GET(options.BaseURL+"/admin/parking-lots", wrapper.GetAdminParkingLots)
	router.POST(options.BaseURL+"/admin/parking-lots", wrapper.PostAdminParkingLots)
	router.DELETE(options.BaseURL+"/admin/parking-lots/:lotId", wrapper.DeleteAdminParkingLotsLotId)
	router.GET(options.BaseURL+"/admin/parking-lots/:lotId", wrapper.GetAdminParkingLotsLotId)
	router.PUT(options.BaseURL+"/admin/parking-lots/:lotId", wrapper.PutAdminParkingLotsLotId)
	router.GET(options.BaseURL+"/admin/parking-lots/:lotId/pricing-configs", wrapper.GetAdminParkingLotsLotIdPricingConfigs)
	router.POST(options.BaseURL+"/admin/parking-lots/:lotId/pricing-configs", wrapper.PostAdminParkingLotsLotIdPricingConfigs)
	router.DELETE(options.BaseURL+"/admin/parking-lots/:lotId/pricing-configs/:configId", wrapper.DeleteAdminParkingLotsLotIdPricingConfigsConfigId)
	router.GET(options.BaseURL+"/admin/parking-lots/:lotId/pricing-configs/:configId", wrapper.GetAdminParkingLotsLotIdPricingConfigsConfigId)
	router.PUT(options.BaseURL+"/admin/parking-lots/:lotId/pricing-configs/:configId", wrapper.PutAdminParkingLotsLotIdPricingConfigsConfigId)
	router.POST(options.BaseURL+"/admin/parking-lots/:lotId/pricing-configs/:configId/activate", wrapper.PostAdminParkingLotsLotIdPricingConfigsConfigIdActivate)
	router.GET(options.BaseURL+"/admin/payments", wrapper.GetAdminPayments)
	router.GET(options.BaseURL+"/admin/payments/:paymentId", wrapper.GetAdminPaymentsPaymentId)
	router.POST(options.BaseURL+"/admin/payments/:paymentId/refund", wrapper.PostAdminPaymentsPaymentIdRefund)
	router.GET(options.BaseURL+"/admin/plates", wrapper.GetAdminPlates)
	router.DELETE(options.BaseURL+"/admin/plates/:plateId", wrapper.DeleteAdminPlatesPlateId)
	router.GET(options.BaseURL+"/admin/plates/:plateId", wrapper.GetAdminPlatesPlateId)
	router.POST(options.BaseURL+"/admin/plates/:plateId/activate", wrapper.PostAdminPlatesPlateIdActivate)
	router.POST(options.BaseURL+"/admin/plates/:plateId/deactivate", wrapper.PostAdminPlatesPlateIdDeactivate)
	router.POST(options.BaseURL+"/admin/pricing-configs/calculate-preview", wrapper.PostAdminPricingConfigsCalculatePreview)
	router.POST(options.BaseURL+"/admin/pricing-configs/validate", wrapper.PostAdminPricingConfigsValidate)
	router.GET(options.BaseURL+"/admin/sessions", wrapper.GetAdminSessions)
	router.GET(options.BaseURL+"/admin/sessions/:sessionId", wrapper.GetAdminSessionsSessionId)
	router.PUT(options.BaseURL+"/admin/sessions/:sessionId", wrapper.PutAdminSessionsSessionId)
	router.POST(options.BaseURL+"/admin/sessions/:sessionId/cancel", wrapper.PostAdminSessionsSessionIdCancel)
	router.POST(options.BaseURL+"/admin/sessions/:sessionId/complete", wrapper.PostAdminSessionsSessionIdComplete)
	router.GET(options.BaseURL+"/admin/users", wrapper.GetAdminUsers)
	router.GET(options.BaseURL+"/admin/users/:userId", wrapper.GetAdminUsersUserId)
	router.PUT(options.BaseURL+"/admin/users/:userId", wrapper.PutAdminUsersUserId)
	router.POST(options.BaseURL+"/admin/users/:userId/activate", wrapper.PostAdminUsersUserIdActivate)
	router.POST(options.BaseURL+"/admin/users/:userId/suspend", wrapper.PostAdminUsersUserIdSuspend)
	router.POST(options.BaseURL+"/auth/change-password", wrapper.PostAuthChangePassword)
	router.POST(options.BaseURL+"/auth/forgot-password", wrapper.PostAuthForgotPassword)
	router.POST(options.BaseURL+"/auth/login", wrapper.PostAuthLogin)
	router.POST(options.BaseURL+"/auth/refresh", wrapper.PostAuthRefresh)
	router.POST(options.BaseURL+"/auth/register", wrapper.PostAuthRegister)
	router.POST(options.BaseURL+"/auth/resend-verification", wrapper.PostAuthResendVerification)
	router.POST(options.BaseURL+"/auth/reset-password", wrapper.PostAuthResetPassword)
	router.GET(options.BaseURL+"/auth/sessions", wrapper.GetAuthSessions)
	router.POST(options.BaseURL+"/auth/sessions/logout-all", wrapper.PostAuthSessionsLogoutAll)
	router.DELETE(options.BaseURL+"/auth/sessions/:sessionId", wrapper.DeleteAuthSessionsSessionId)
	router.GET(options.BaseURL+"/auth/verify-email", wrapper.GetAuthVerifyEmail)
	router.GET(options.BaseURL+"/bookings", wrapper.GetBookings)
	router.POST(options.BaseURL+"/bookings", wrapper.PostBookings)
	router.DELETE(options.BaseURL+"/bookings/:bookingId", wrapper.DeleteBookingsBookingId)
	router.GET(options.BaseURL+"/bookings/:bookingId", wrapper.GetBookingsBookingId)
	router.POST(options.BaseURL+"/hardware/detection", wrapper.PostHardwareDetection)
	router.GET(options.BaseURL+"/notifications", wrapper.GetNotifications)
	router.PATCH(options.BaseURL+"/notifications/:notificationId/mark-read", wrapper.PatchNotificationsNotificationIdMarkRead)
	router.GET(options.BaseURL+"/parking-lots", wrapper.GetParkingLots)
	router.GET(options.BaseURL+"/parking-lots/:lotId", wrapper.GetParkingLotsLotId)
	router.GET(options.BaseURL+"/parking-lots/:lotId/availability", wrapper.GetParkingLotsLotIdAvailability)
	router.GET(options.BaseURL+"/payment-methods", wrapper.GetPaymentMethods)
	router.POST(options.BaseURL+"/payment-methods", wrapper.PostPaymentMethods)
	router.POST(options.BaseURL+"/payment-methods/stripe-callback", wrapper.PostPaymentMethodsStripeCallback)
	router.GET(options.BaseURL+"/payment-methods/validate-setup", wrapper.GetPaymentMethodsValidateSetup)
	router.DELETE(options.BaseURL+"/payment-methods/:paymentMethodId", wrapper.DeletePaymentMethodsPaymentMethodId)
	router.POST(options.BaseURL+"/payment-methods/:paymentMethodId/set-default", wrapper.PostPaymentMethodsPaymentMethodIdSetDefault)
	router.GET(options.BaseURL+"/payments", wrapper.GetPayments)
	router.POST(options.BaseURL+"/payments/webhooks/stripe", wrapper.PostPaymentsWebhooksStripe)
	router.POST(options.BaseURL+"/payments/:sessionId/create-payment-link", wrapper.PostPaymentsSessionIdCreatePaymentLink)
	router.POST(options.BaseURL+"/payments/:sessionId/process-auto-payment", wrapper.PostPaymentsSessionIdProcessAutoPayment)
	router.GET(options.BaseURL+"/sessions", wrapper.GetSessions)
	router.GET(options.BaseURL+"/sessions/active", wrapper.GetSessionsActive)
	router.GET(options.BaseURL+"/sessions/:sessionId", wrapper.GetSessionsSessionId)
	router.GET(options.BaseURL+"/users/plates", wrapper.GetUsersPlates)
	router.POST(options.BaseURL+"/users/plates", wrapper.PostUsersPlates)
	router.DELETE(options.BaseURL+"/users/plates/:plateId", wrapper.DeleteUsersPlatesPlateId)
	router.GET(options.BaseURL+"/users/profile", wrapper.GetUsersProfile)
	router.PUT(options.BaseURL+"/users/profile", wrapper.PutUsersProfile)
}
